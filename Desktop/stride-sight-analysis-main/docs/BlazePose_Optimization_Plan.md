# BlazePose 3D Pipeline Optimization Plan

## Issue Summary
1. **No Pose Detection**: BlazePose consistently fails to detect poses despite using the Full model in a front-facing Treadmill setup
2. **NaN Coordinate Values**: Extensive NaN values in both 2D keypoints and 3D world landmarks
3. **Memory Leaks**: High GPU memory usage (822.87 MB) indicating memory leaks
4. **Tensor Processing Issues**: Potential problems in the tensor processing pipeline
5. **Model Configuration**: Possible incorrect configuration of the BlazePose model

## Implementation Plan

### Phase 1: Fix BlazePose Model Configuration and Initialization
**Goal**: Ensure proper model initialization and configuration for the Treadmill setup

1. **Update BlazePose Configuration**:
   - Verify and update model parameters for the Full model
   - Ensure proper runtime flags are set for WebGL acceleration
   - Implement proper model loading with progress indicators

2. **Optimize Model Selection Logic**:
   - Simplify the "intelligent model selection" logic
   - Ensure consistent model quality for Treadmill setup
   - Add proper error handling for model loading failures

3. **Implement Model Warm-up**:
   - Add a warm-up phase to initialize the model with sample frames
   - Ensure the model is fully loaded before processing video frames
   - Add proper loading state management

### Phase 2: Fix Tensor Processing Pipeline
**Goal**: Ensure proper tensor processing and coordinate transformation

1. **Refactor Tensor Processing**:
   - Simplify the tensor processing pipeline
   - Ensure proper tensor shape validation
   - Implement proper error handling for tensor operations

2. **Fix Coordinate Transformation**:
   - Ensure proper transformation between normalized and pixel coordinates
   - Fix the world landmark projection for 3D coordinates
   - Implement proper validation for coordinate ranges

3. **Optimize NaN Handling**:
   - Implement more robust NaN detection and repair
   - Add fallback mechanisms for missing keypoints
   - Improve the anatomical position generator for back-facing poses

### Phase 3: Implement Memory Management
**Goal**: Fix memory leaks and optimize performance

1. **Enhance Tensor Disposal**:
   - Implement aggressive tensor cleanup
   - Add memory usage monitoring
   - Implement batch processing to reduce memory pressure

2. **Optimize Rendering Pipeline**:
   - Implement frame skipping for performance optimization
   - Reduce unnecessary re-renders
   - Optimize canvas operations

3. **Implement Resource Management**:
   - Add proper cleanup on component unmount
   - Implement detector reset mechanism
   - Add memory usage logging

### Phase 4: Enhance Error Handling and Debugging
**Goal**: Improve error detection and reporting

1. **Implement Comprehensive Error Logging**:
   - Add detailed error reporting for each pipeline stage
   - Implement error classification for common issues
   - Add user-friendly error messages

2. **Add Visual Debugging Tools**:
   - Implement visual indicators for pose quality
   - Add debug overlays for tensor processing
   - Implement frame capture for problematic frames

3. **Implement Fallback Mechanisms**:
   - Add graceful degradation for partial pose detection
   - Implement pose interpolation for missing frames
   - Add recovery mechanisms for detection failures

## Implementation Timeline

1. **Phase 1 (Days 1-2)**: Fix BlazePose Model Configuration and Initialization
2. **Phase 2 (Days 3-4)**: Fix Tensor Processing Pipeline
3. **Phase 3 (Days 5-6)**: Implement Memory Management
4. **Phase 4 (Days 7-8)**: Enhance Error Handling and Debugging
5. **Testing and Refinement (Days 9-10)**: Comprehensive testing and bug fixes

## Implementation Details

### Phase 1: Fix BlazePose Model Configuration and Initialization

#### Step 1.1: Update BlazePose Configuration in useBlazePoseDetection.ts

```typescript
// Update model configuration for better performance and reliability
const blazePoseConfig: poseDetection.BlazePoseModelConfig = {
  runtime: 'tfjs',
  enableSmoothing: true,
  modelType: modelQuality.toLowerCase() as 'lite' | 'full' | 'heavy',
  solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/pose',
  detectorModelUrl: undefined, // Let the library use the default URL
  landmarkModelUrl: undefined, // Let the library use the default URL
  enableSegmentation: false, // Disable segmentation for better performance
  smoothSegmentation: false,
  minPoseScore: 0.2, // Lower threshold for better detection
  multiPoseMaxDimension: 256, // Optimize for single pose
  enableFaceKeypoints: true, // Important for 3D landmarks
  selfieMode: false, // Important for correct orientation
  cameraFps: 30, // Match video FPS
};
```

#### Step 1.2: Implement Model Warm-up

```typescript
// Add model warm-up function
const warmUpModel = async (detector: poseDetection.PoseDetector): Promise<boolean> => {
  try {
    console.log('🔥 WARM-UP: Starting BlazePose model warm-up...');
    
    // Create a small blank canvas for warm-up
    const canvas = document.createElement('canvas');
    canvas.width = 256;
    canvas.height = 256;
    const ctx = canvas.getContext('2d');
    if (!ctx) return false;
    
    // Fill with gray color
    ctx.fillStyle = '#888888';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // Run inference on blank canvas
    const warmupResult = await detector.estimatePoses(
      canvas, 
      { flipHorizontal: false }
    );
    
    console.log('✅ WARM-UP: BlazePose model warm-up complete', warmupResult);
    return true;
  } catch (error) {
    console.error('❌ WARM-UP: BlazePose model warm-up failed:', error);
    return false;
  }
};
```

#### Step 1.3: Enhance Model Initialization

```typescript
// Enhanced model initialization with proper error handling
const initializeDetector = async () => {
  if (detector) return;
  
  setIsInitializing(true);
  console.log('🔄 PHASE 4 ENHANCED: Initializing BlazePose with coordinate fix...');
  console.log('🎯 PHASE 4 ENHANCED: Model Quality:', modelQuality);
  
  try {
    // Ensure TensorFlow.js backend is ready
    await tf.ready();
    const backend = tf.getBackend();
    console.log(`✅ ${backend} backend ready for Phase 4 BlazePose (optimized)`);
    
    // Log backend info for debugging
    console.log('📊 TensorFlow.js Backend Info:', {
      backend: backend,
      webgl: backend === 'webgl' ? 'enabled' : 'disabled',
      version: tf.version.tfjs
    });
    
    // Set up BlazePose configuration
    const isBackFacing = videoSetup?.toLowerCase().includes('out') || 
                         videoSetup?.toLowerCase().includes('offset');
    
    // Use intelligent model selection
    let effectiveModelQuality = modelQuality;
    if (isBackFacing && modelQuality === 'Full') {
      effectiveModelQuality = 'Lite';
      console.log('🔧 INTELLIGENT MODEL: Using BlazePose Lite for back-facing scenario:', videoSetup);
    } else {
      console.log('🔧 INTELLIGENT MODEL: Using BlazePose', modelQuality, 'for front-facing scenario:', videoSetup);
    }
    
    // Configure BlazePose
    const blazePoseConfig: poseDetection.BlazePoseModelConfig = {
      runtime: 'tfjs',
      enableSmoothing: true,
      modelType: effectiveModelQuality.toLowerCase() as 'lite' | 'full' | 'heavy',
      solutionPath: 'https://cdn.jsdelivr.net/npm/@mediapipe/pose',
      detectorModelUrl: undefined,
      landmarkModelUrl: undefined,
      enableSegmentation: false,
      smoothSegmentation: false,
      minPoseScore: 0.2,
      multiPoseMaxDimension: 256,
      enableFaceKeypoints: true,
      selfieMode: false,
      cameraFps: 30,
    };
    
    console.log('🔧 PHASE 4 ENHANCED: BlazePose config:', blazePoseConfig);
    
    // Create detector
    const blazePoseDetector = await poseDetection.createDetector(
      poseDetection.SupportedModels.BlazePose,
      blazePoseConfig
    );
    
    console.log('✅ PHASE 4 ENHANCED: BlazePose detector created with coordinate fixes');
    
    // Warm up the model
    const warmUpSuccess = await warmUpModel(blazePoseDetector);
    
    if (warmUpSuccess) {
      setDetector(blazePoseDetector);
      setIsInitialized(true);
      console.log('✅ PHASE 4 ENHANCED: BlazePose initialization complete');
    } else {
      console.error('❌ PHASE 4 ENHANCED: BlazePose warm-up failed, retrying...');
      // Retry initialization after a delay
      setTimeout(initializeDetector, 1000);
    }
  } catch (error) {
    console.error('❌ PHASE 4 ENHANCED: BlazePose initialization error:', error);
    setDebugInfo(`BlazePose Init Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    setIsInitializing(false);
  }
};
```

### Phase 2: Fix Tensor Processing Pipeline

#### Step 2.1: Enhance detectPoses Function

```typescript
const detectPoses = async (video: HTMLVideoElement): Promise<poseDetection.Pose[]> => {
  if (!detector || !isInitialized) {
    console.log('⚠️ PHASE 4 ENHANCED: BlazePose detector not initialized');
    return [];
  }
  
  const startTime = performance.now();
  
  try {
    // Skip frames if processing is taking too long
    if (isProcessing.current) {
      if (shouldLog(300)) {
        console.log('⏭️ PHASE 4 ENHANCED: Skipping frame - previous frame still processing');
      }
      return lastPoseResult.current;
    }
    
    isProcessing.current = true;
    frameCount.current++;
    
    // Get video dimensions
    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;
    
    if (videoWidth === 0 || videoHeight === 0) {
      console.log('⚠️ PHASE 4 ENHANCED: Video dimensions not available');
      isProcessing.current = false;
      return [];
    }
    
    // Log video info occasionally
    if (shouldLog(300)) {
      console.log('🎥 PHASE 4 ENHANCED: Processing video frame:', {
        frame: frameCount.current,
        dimensions: `${videoWidth}x${videoHeight}`,
        videoTime: video.currentTime.toFixed(2),
        modelQuality: modelQuality
      });
    }
    
    // Estimate poses with proper error handling
    const poses = await detector.estimatePoses(video, {
      flipHorizontal: false,
      maxPoses: 1,
      scoreThreshold: 0.2
    });
    
    // Process results
    if (poses.length > 0) {
      const pose = poses[0];
      
      // Validate pose quality
      const poseQuality = validatePoseQuality(pose, videoSetup);
      
      if (!poseQuality.isValid) {
        console.log(`⚠️ POSE VALIDATION: Low quality pose detected: ${poseQuality.reason}`);
        
        // Apply coordinate fixes even for low-quality poses
        const imageSize = { width: videoWidth, height: videoHeight };
        const processedPose = await processBlazePoseWithCoordinateFixes(pose, imageSize);
        
        // Track performance
        const processingTime = performance.now() - startTime;
        updatePerformanceMetrics(processingTime);
        
        isProcessing.current = false;
        lastPoseResult.current = [processedPose];
        return [processedPose];
      }
      
      // Process high-quality pose
      const imageSize = { width: videoWidth, height: videoHeight };
      const processedPose = await processBlazePoseWithCoordinateFixes(pose, imageSize);
      
      // Track performance
      const processingTime = performance.now() - startTime;
      updatePerformanceMetrics(processingTime);
      
      isProcessing.current = false;
      lastPoseResult.current = [processedPose];
      return [processedPose];
    } else {
      // Track performance even for empty results
      const processingTime = performance.now() - startTime;
      updatePerformanceMetrics(processingTime);
      lastPoseResult.current = [];
      
      if (shouldLog(180)) {
        console.log('⚠️ PHASE 4 ENHANCED: No poses detected');
      }
      
      isProcessing.current = false;
      return [];
    }
  } catch (error) {
    // Track performance even for errors
    const processingTime = performance.now() - startTime;
    updatePerformanceMetrics(processingTime);
    
    console.error('❌ PHASE 4 ENHANCED: BlazePose detection error:', error);
    setDebugInfo(`Phase 4 BlazePose Detection Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    
    isProcessing.current = false;
    return [];
  }
};
```

### Phase 3: Implement Memory Management

#### Step 3.1: Add Memory Monitoring and Cleanup

```typescript
// Add memory monitoring
const monitorMemoryUsage = () => {
  if (typeof (performance as any).memory !== 'undefined') {
    const memoryInfo = (performance as any).memory;
    console.log('📊 MEMORY USAGE:', {
      totalJSHeapSize: (memoryInfo.totalJSHeapSize / (1024 * 1024)).toFixed(2) + ' MB',
      usedJSHeapSize: (memoryInfo.usedJSHeapSize / (1024 * 1024)).toFixed(2) + ' MB',
      jsHeapSizeLimit: (memoryInfo.jsHeapSizeLimit / (1024 * 1024)).toFixed(2) + ' MB'
    });
  }
  
  // Log TensorFlow.js memory
  if (tf && typeof tf.memory === 'function') {
    const tfMemory = tf.memory();
    console.log('📊 TENSORFLOW.JS MEMORY:', {
      numTensors: tfMemory.numTensors,
      numDataBuffers: tfMemory.numDataBuffers,
      unreliable: tfMemory.unreliable,
      reasons: tfMemory.reasons
    });
  }
};

// Enhanced tensor cleanup
const cleanupTensors = () => {
  try {
    // Dispose all non-persistent tensors
    tf.disposeVariables();
    
    // Force garbage collection if available
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    }
    
    console.log('🧹 MEMORY CLEANUP: TensorFlow.js tensors disposed');
  } catch (error) {
    console.warn('⚠️ MEMORY CLEANUP: Error during tensor cleanup:', error);
  }
};

// Add periodic cleanup
useEffect(() => {
  const cleanupInterval = setInterval(() => {
    monitorMemoryUsage();
    cleanupTensors();
  }, 30000); // Every 30 seconds
  
  return () => {
    clearInterval(cleanupInterval);
  };
}, []);
```

### Phase 4: Enhance Error Handling and Debugging

#### Step 4.1: Implement Comprehensive Error Logging

```typescript
// Error classification and logging
const logPoseError = (error: any, stage: string, details?: any) => {
  // Classify error
  let errorType = 'Unknown';
  let errorSeverity = 'warning';
  
  if (error instanceof Error) {
    if (error.message.includes('memory')) {
      errorType = 'MemoryError';
      errorSeverity = 'critical';
    } else if (error.message.includes('tensor')) {
      errorType = 'TensorError';
      errorSeverity = 'high';
    } else if (error.message.includes('model')) {
      errorType = 'ModelError';
      errorSeverity = 'high';
    } else if (error.message.includes('WebGL')) {
      errorType = 'WebGLError';
      errorSeverity = 'critical';
    }
  }
  
  // Log with appropriate level
  const errorInfo = {
    type: errorType,
    severity: errorSeverity,
    stage: stage,
    message: error instanceof Error ? error.message : String(error),
    details: details,
    timestamp: new Date().toISOString()
  };
  
  if (errorSeverity === 'critical') {
    console.error('❌ CRITICAL ERROR:', errorInfo);
  } else if (errorSeverity === 'high') {
    console.error('❌ ERROR:', errorInfo);
  } else {
    console.warn('⚠️ WARNING:', errorInfo);
  }
  
  // Update debug info
  setDebugInfo(`Error in ${stage}: ${errorInfo.message}`);
  
  // Return error info for potential reporting
  return errorInfo;
};
```