
import { PoseKeypoint } from './poseRenderer';

export interface BiometricData {
  frameNumber: number;
  timestamp: number;
  keypoints: PoseKeypoint[];
  // Future biomechanics will be added here
}

export const processPoseData = (
  poses: any[], 
  frameCount: number
): BiometricData | null => {
  if (poses.length === 0) return null;

  const pose = poses[0];
  
  return {
    frameNumber: frameCount,
    timestamp: Date.now(),
    keypoints: pose.keypoints.map((kp: any) => ({
      x: kp.x,
      y: kp.y,
      score: kp.score ?? 0 // Handle optional score property
    }))
    // Future: Add stride analysis, cadence, ground contact time, etc.
  };
};

// Placeholder for future biomechanics calculations
export const calculateRunningMetrics = (poseData: BiometricData) => {
  // Future implementation will include:
  // - Stride length
  // - Cadence
  // - Ground contact time
  // - Vertical oscillation
  // - Foot strike pattern
  // - Joint angles
  return {};
};
