
export interface PoseKeypoint {
  x: number;
  y: number;
  score: number;
}

export interface ScaledPose {
  keypoints: PoseKeypoint[];
}

export const scaleKeypoints = (
  keypoints: PoseKeypoint[],
  displayWidth: number,
  displayHeight: number,
  videoWidth: number,
  videoHeight: number,
  userHeightMeters?: number
): PoseKeypoint[] => {
  const scaleX = displayWidth / videoWidth;
  const scaleY = displayHeight / videoHeight;
  
  return keypoints.map((kp) => ({
    ...kp,
    x: kp.x * scaleX,
    y: kp.y * scaleY
  }));
};

// POSE DIRECTION DETECTION: Determine if person is facing left or right
const detectPoseDirection = (keypoints: PoseKeypoint[]): 'left-facing' | 'right-facing' | 'unknown' => {
  try {
    if (!keypoints || keypoints.length < 17) return 'unknown';
    
    // Method 1: Compare shoulder visibility/confidence
    const leftShoulder = keypoints[5];   // left_shoulder
    const rightShoulder = keypoints[6];  // right_shoulder
    const leftHip = keypoints[11];       // left_hip
    const rightHip = keypoints[12];      // right_hip
    
    // Calculate average confidence for each side
    const leftSideConfidence = [leftShoulder, leftHip].filter(kp => kp && kp.score > 0.1)
      .reduce((sum, kp) => sum + kp.score, 0) / 2;
    
    const rightSideConfidence = [rightShoulder, rightHip].filter(kp => kp && kp.score > 0.1)
      .reduce((sum, kp) => sum + kp.score, 0) / 2;
    
    // Method 2: Check relative positioning (shoulder width comparison)
    let shoulderWidthFactor = 0;
    if (leftShoulder && rightShoulder && leftShoulder.score > 0.3 && rightShoulder.score > 0.3) {
      // If left shoulder is more to the left than right shoulder is to the right, person is likely left-facing
      const shoulderCenterX = (leftShoulder.x + rightShoulder.x) / 2;
      const leftDistance = Math.abs(leftShoulder.x - shoulderCenterX);
      const rightDistance = Math.abs(rightShoulder.x - shoulderCenterX);
      shoulderWidthFactor = leftDistance - rightDistance; // Positive = left side more visible
    }
    
    // Combine confidence and positioning
    const confidenceDiff = leftSideConfidence - rightSideConfidence;
    const combinedScore = confidenceDiff + (shoulderWidthFactor * 0.001); // Slight weight to positioning
    
    // Determine direction with threshold
    if (combinedScore > 0.1) {
      return 'left-facing';  // Left side more visible = person facing left
    } else if (combinedScore < -0.1) {
      return 'right-facing'; // Right side more visible = person facing right
    } else {
      return 'unknown';      // Too close to call
    }
  } catch (error) {
    console.warn('⚠️ Error detecting pose direction:', error);
    return 'unknown';
  }
};

// ENHANCED: Medical-grade skeleton with view-specific rendering
export const drawPoseConnections = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[],
  viewType: 'side' | 'rear' = 'side'
) => {
  try {
    if (!keypoints || keypoints.length < 17) {
      console.warn('⚠️ Insufficient keypoints for pose connections');
      return;
    }

    // PHASE 1.1: Extend keypoints with enhanced heel and foot estimation
    const extendedKeypoints = estimateHeelAndFoot(keypoints);

    // DETECT POSE DIRECTION: Determine which side is primary (closer to camera)
    const poseDirection = detectPoseDirection(keypoints);
    
    // Standard MoveNet connections grouped by body side
    const coreConnections = [
      [5, 6],   // shoulders
      [11, 12], // hips
      [5, 11],  // left torso
      [6, 12],  // right torso
    ];
    
    const leftSideConnections = [
      [5, 7], [7, 9],   // left arm
      [11, 13], [13, 15] // left leg
    ];

    const rightSideConnections = [
      [6, 8], [8, 10],   // right arm
      [12, 14], [14, 16] // right leg
    ];

    // ENHANCED: Improved foot connections with proper foot shape
    const footConnections = [];
    if (extendedKeypoints.length >= 25) { // Original 17 + 8 foot points (4 per foot)
      footConnections.push(
        // Left foot: ankle -> heel -> midfoot -> forefoot -> toes
        [15, 17], [17, 18], [18, 19], [19, 20],
        // Right foot: ankle -> heel -> midfoot -> forefoot -> toes
        [16, 21], [21, 22], [22, 23], [23, 24]
      );
    }

    // PHASE 1.1: Enhanced medical styling with increased thickness
    ctx.strokeStyle = '#00FF41'; // Medical green
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Add subtle shadow for depth
    ctx.shadowColor = 'rgba(0, 0, 0, 0.6)';
    ctx.shadowBlur = 1;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;

    let connectionsDrawn = 0;

    // Draw core connections (always full opacity) - PHASE 1.1: Increased thickness
    ctx.globalAlpha = 1.0;
    ctx.lineWidth = 3.5; // Increased from 2.5 to 3.5
    
    coreConnections.forEach(([i, j]) => {
      const kp1 = keypoints[i];
      const kp2 = keypoints[j];
      
      if (kp1 && kp2 && kp1.score > 0.3 && kp2.score > 0.3) {
        ctx.beginPath();
        ctx.moveTo(kp1.x, kp1.y);
        ctx.lineTo(kp2.x, kp2.y);
        ctx.stroke();
        connectionsDrawn++;
      }
    });
    
    // REFINED: Selective side connection drawing with arm filtering
    
    // VIEW-SPECIFIC: Apply depth perception for side view, equal treatment for rear view
    const drawSideConnectionsSelective = (connections: number[][], isPrimary: boolean, includeArms: boolean = true) => {
      // For rear view, treat all connections equally
      if (viewType === 'rear') {
        ctx.globalAlpha = 1.0;
        ctx.lineWidth = 3.5;
        ctx.strokeStyle = '#00FF41'; // Medical green for all connections
      } else {
        // Side view: apply depth perception
        ctx.globalAlpha = isPrimary ? 1.0 : 0.35;
        ctx.lineWidth = isPrimary ? 3.5 : 3.0;

        // Set color based on primary/secondary and connection type
        if (isPrimary) {
          ctx.strokeStyle = '#00FF41'; // Medical green for primary
        } else {
          ctx.strokeStyle = '#228B22'; // Darker green for secondary legs
        }
      }

      connections.forEach(([i, j]) => {
        const kp1 = keypoints[i];
        const kp2 = keypoints[j];

        // Skip arm connections for secondary side (side view only)
        const isArmConnection = (i >= 5 && i <= 10) || (j >= 5 && j <= 10);
        if (viewType === 'side' && !isPrimary && isArmConnection && !includeArms) {
          return; // Skip secondary arm connections in side view
        }

        if (kp1 && kp2 && kp1.score > 0.3 && kp2.score > 0.3) {
          ctx.beginPath();
          ctx.moveTo(kp1.x, kp1.y);
          ctx.lineTo(kp2.x, kp2.y);
          ctx.stroke();
          connectionsDrawn++;
        }
      });
    };

    if (poseDirection === 'left-facing') {
      // Person facing left: left side is primary (closer), right side is secondary (farther)
      drawSideConnectionsSelective(leftSideConnections, true, true);   // Primary with arms
      drawSideConnectionsSelective(rightSideConnections, false, false); // Secondary without arms
      console.log('👤 LEFT-FACING: Left side primary with arms, right side legs only');
    } else if (poseDirection === 'right-facing') {
      // Person facing right: right side is primary (closer), left side is secondary (farther)
      drawSideConnectionsSelective(rightSideConnections, true, true);  // Primary with arms
      drawSideConnectionsSelective(leftSideConnections, false, false);  // Secondary without arms
      console.log('👤 RIGHT-FACING: Right side primary with arms, left side legs only');
    } else {
      // Unknown direction: show both sides but still hide secondary arms
      drawSideConnectionsSelective(leftSideConnections, true, true);   // Assume left primary
      drawSideConnectionsSelective(rightSideConnections, false, false); // Right secondary without arms
      console.log('👤 UNKNOWN DIRECTION: Left side with arms, right side legs only');
    }

    // PHASE 1.1: Draw enhanced foot connections
    if (footConnections.length > 0) {
      ctx.globalAlpha = 0.8; // Slightly dimmed for foot details
      ctx.lineWidth = 2.5;
      ctx.strokeStyle = '#40E0D0'; // Turquoise for foot details

      footConnections.forEach(([i, j]) => {
        const kp1 = extendedKeypoints[i];
        const kp2 = extendedKeypoints[j];

        if (kp1 && kp2 && kp1.score > 0.3 && kp2.score > 0.3) {
          ctx.beginPath();
          ctx.moveTo(kp1.x, kp1.y);
          ctx.lineTo(kp2.x, kp2.y);
          ctx.stroke();
          connectionsDrawn++;
        }
      });
    }
    
    // Reset drawing state
    ctx.globalAlpha = 1.0;
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    
    console.log(`🏥 MEDICAL SKELETON: Drew ${connectionsDrawn} connections with direction-based depth (${poseDirection})`);
  } catch (error) {
    console.error('🚨 Error drawing pose connections:', error);
  }
};

// ENHANCED: Improved foot detection with proper foot shape rendering
const estimateHeelAndFoot = (keypoints: PoseKeypoint[]): PoseKeypoint[] => {
  if (!keypoints || keypoints.length < 17) return keypoints;

  const extended = [...keypoints];

  // Enhanced anatomical ratios for realistic foot proportions
  const FOOT_RATIOS = {
    ankleToHeel: 0.15,      // 15% of leg length for heel offset
    heelToMidfoot: 0.12,    // 12% for midfoot
    midfootToForefoot: 0.15, // 15% for forefoot
    forefootToToes: 0.10    // 10% for toe extension
  };

  // Process both feet with enhanced foot shape detection
  [15, 16].forEach((ankleIndex) => {
    const ankle = keypoints[ankleIndex];
    const knee = keypoints[ankleIndex - 2];
    const hip = keypoints[ankleIndex - 4];

    if (ankle && knee && ankle.score > 0.3 && knee.score > 0.3) {
      // Calculate leg direction and foot orientation
      const legDx = ankle.x - knee.x;
      const legDy = ankle.y - knee.y;
      const legLength = Math.sqrt(legDx * legDx + legDy * legDy);

      if (legLength > 0) {
        // Normalize leg direction
        const legUnitX = legDx / legLength;
        const legUnitY = legDy / legLength;

        // Calculate foot perpendicular for width
        const footPerp = { x: -legUnitY, y: legUnitX };
        const footWidth = legLength * 0.08; // 8% of leg length for foot width

        // HEEL: Behind ankle with slight ground contact adjustment
        const heelOffset = legLength * FOOT_RATIOS.ankleToHeel;
        const heelX = ankle.x - (legUnitX * heelOffset);
        const heelY = ankle.y - (legUnitY * heelOffset) + (legLength * 0.03);

        // MIDFOOT: Between heel and forefoot
        const midfootOffset = legLength * FOOT_RATIOS.heelToMidfoot;
        const midfootX = heelX + (legUnitX * midfootOffset);
        const midfootY = heelY + (legUnitY * midfootOffset) + (legLength * 0.02);

        // FOREFOOT: Main foot body
        const forefootOffset = legLength * FOOT_RATIOS.midfootToForefoot;
        const forefootX = midfootX + (legUnitX * forefootOffset);
        const forefootY = midfootY + (legUnitY * forefootOffset) + (legLength * 0.01);

        // TOES: Extended from forefoot
        const toeOffset = legLength * FOOT_RATIOS.forefootToToes;
        const toeX = forefootX + (legUnitX * toeOffset);
        const toeY = forefootY + (legUnitY * toeOffset);

        // Add foot points with high confidence
        extended.push({
          x: heelX,
          y: heelY,
          score: ankle.score * 0.90 // High confidence for heel
        });

        extended.push({
          x: midfootX,
          y: midfootY,
          score: ankle.score * 0.85 // Good confidence for midfoot
        });

        extended.push({
          x: forefootX,
          y: forefootY,
          score: ankle.score * 0.85 // Good confidence for forefoot
        });

        extended.push({
          x: toeX,
          y: toeY,
          score: ankle.score * 0.80 // Good confidence for toes
        });
      }
    } else {
      // Add placeholder points if ankle/knee not detected
      extended.push({ x: 0, y: 0, score: 0 }); // heel
      extended.push({ x: 0, y: 0, score: 0 }); // midfoot
      extended.push({ x: 0, y: 0, score: 0 }); // forefoot
      extended.push({ x: 0, y: 0, score: 0 }); // toes
    }
  });

  return extended;
};

// COLOR-CODED KEYPOINTS: View-specific rendering with enhanced foot tracking
export const drawPoseKeypoints = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[],
  viewType: 'side' | 'rear' = 'side'
) => {
  try {
    if (!keypoints || keypoints.length < 17) {
      console.warn('⚠️ Insufficient keypoints for pose rendering');
      return;
    }

    // Extend keypoints with foot data for complete tracking
    const extendedKeypoints = estimateHeelAndFoot(keypoints);

    // DETECT POSE DIRECTION: Determine which side is primary
    const poseDirection = detectPoseDirection(keypoints);

    // COLOR-CODED JOINT PAIRS: Enhanced tracking with specific colors for each joint type
    const medicalKeypoints = [
      // HEAD/NECK (clinical reference only)
      { index: 0, name: 'nose', side: 'center', priority: 'low', size: 4, color: '#40E0D0' }, // Turquoise
      { index: 3, name: 'left_ear', side: 'left', priority: 'low', size: 4, color: '#FFD700' }, // Gold
      { index: 4, name: 'right_ear', side: 'right', priority: 'low', size: 4, color: '#FFD700' }, // Gold

      // UPPER BODY with color-coded pairs
      { index: 5, name: 'left_shoulder', side: 'left', priority: 'medium', size: 6, color: '#FFA500' }, // Orange
      { index: 6, name: 'right_shoulder', side: 'right', priority: 'medium', size: 6, color: '#FFA500' }, // Orange
      { index: 7, name: 'left_elbow', side: 'left', priority: 'medium', size: 6, color: '#FFFF00' }, // Yellow
      { index: 8, name: 'right_elbow', side: 'right', priority: 'medium', size: 6, color: '#FFFF00' }, // Yellow
      { index: 9, name: 'left_wrist', side: 'left', priority: 'medium', size: 5, color: '#FFFFFF' }, // White
      { index: 10, name: 'right_wrist', side: 'right', priority: 'medium', size: 5, color: '#FFFFFF' }, // White

      // LOWER BODY with color-coded pairs (highest priority for gait analysis)
      { index: 11, name: 'left_hip', side: 'left', priority: 'high', size: 7, color: '#0000FF' }, // Blue
      { index: 12, name: 'right_hip', side: 'right', priority: 'high', size: 7, color: '#0000FF' }, // Blue
      { index: 13, name: 'left_knee', side: 'left', priority: 'high', size: 7, color: '#800080' }, // Purple
      { index: 14, name: 'right_knee', side: 'right', priority: 'high', size: 7, color: '#800080' }, // Purple
      { index: 15, name: 'left_ankle', side: 'left', priority: 'high', size: 6, color: '#FF0000' }, // Red
      { index: 16, name: 'right_ankle', side: 'right', priority: 'high', size: 6, color: '#FF0000' } // Red
    ];

    // Extended foot keypoints with color coding
    const footKeypoints = [
      // Left foot
      { index: 17, name: 'left_heel', side: 'left', priority: 'high', size: 4, color: '#FFC0CB' }, // Pink
      { index: 18, name: 'left_midfoot', side: 'left', priority: 'medium', size: 3, color: '#FF69B4' }, // Hot Pink
      { index: 19, name: 'left_forefoot', side: 'left', priority: 'medium', size: 3, color: '#FF1493' }, // Deep Pink
      { index: 20, name: 'left_toes', side: 'left', priority: 'medium', size: 4, color: '#8B0000' }, // Burgundy
      // Right foot
      { index: 21, name: 'right_heel', side: 'right', priority: 'high', size: 4, color: '#FFC0CB' }, // Pink
      { index: 22, name: 'right_midfoot', side: 'right', priority: 'medium', size: 3, color: '#FF69B4' }, // Hot Pink
      { index: 23, name: 'right_forefoot', side: 'right', priority: 'medium', size: 3, color: '#FF1493' }, // Deep Pink
      { index: 24, name: 'right_toes', side: 'right', priority: 'medium', size: 4, color: '#8B0000' } // Burgundy
    ];

    let keypointsDrawn = 0;
    
    // Combine main keypoints with foot keypoints for rendering
    const allKeypoints = [...medicalKeypoints, ...footKeypoints];

    allKeypoints.forEach(({ index, side, priority, size, color }) => {
      const keypoint = extendedKeypoints[index]; // Use extended keypoints for foot data

      if (keypoint && keypoint.score > 0.3) {
        // Determine opacity based on view type and pose direction
        let opacity = 1.0; // Default full opacity

        if (viewType === 'rear') {
          // Rear view: all joints get full opacity
          opacity = 1.0;
        } else if (side !== 'center') {
          // Side view: apply depth perception
          if (poseDirection === 'left-facing') {
            // Left-facing: left side primary (full), right side secondary (dimmed)
            opacity = side === 'left' ? 1.0 : 0.4;
          } else if (poseDirection === 'right-facing') {
            // Right-facing: right side primary (full), left side secondary (dimmed)
            opacity = side === 'right' ? 1.0 : 0.4;
          }
          // If unknown direction, both sides get full opacity
        }

        // COLOR-CODED JOINTS: Use specific colors for each joint type
        let fillColor: string, strokeColor: string;

        // Determine if this joint is on the primary side
        const isJointPrimary = (poseDirection === 'left-facing' && side === 'left') ||
                              (poseDirection === 'right-facing' && side === 'right') ||
                              (poseDirection === 'unknown' && side === 'left') ||
                              side === 'center';

        // Check if this is a secondary leg joint (side view only)
        const isLegJoint = index >= 11 && index <= 16; // Hip, knee, ankle joints
        const isSecondaryLeg = viewType === 'side' && !isJointPrimary && isLegJoint;

        if (isSecondaryLeg) {
          // Gray styling for secondary leg joints (side view only)
          fillColor = '#808080'; // Gray for secondary leg joints
          strokeColor = '#606060';
        } else if (color) {
          // Use color-coded system for better tracking
          fillColor = color;
          strokeColor = color;
        } else {
          // Fallback to standard medical colors
          switch (priority) {
            case 'high':
              fillColor = '#00FF41'; // Medical green for critical joints
              strokeColor = '#00CC33';
              break;
            case 'medium':
              fillColor = '#40E0D0'; // Turquoise for secondary joints
              strokeColor = '#20B2AA';
              break;
            case 'low':
              fillColor = '#FFD700'; // Gold for reference points
              strokeColor = '#DAA520';
              break;
          }
        }
        
        ctx.globalAlpha = opacity;
        
        // Subtle shadow for depth
        ctx.shadowColor = 'rgba(0, 0, 0, 0.6)';
        ctx.shadowBlur = 1;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
        
        // Main joint circle
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, size, 0, 2 * Math.PI);
        ctx.fillStyle = fillColor;
        ctx.fill();
        
        // Reset shadow
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        
        // Precision outline
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, size, 0, 2 * Math.PI);
        ctx.strokeStyle = strokeColor;
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // Small white center for precision
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, size - 2, 0, 2 * Math.PI);
        ctx.fillStyle = '#FFFFFF';
        ctx.fill();
        
        keypointsDrawn++;
      }
    });
    
    // Reset global alpha
    ctx.globalAlpha = 1.0;
    
    console.log(`🏥 MEDICAL JOINTS: Drew ${keypointsDrawn} direction-aware keypoints (${poseDirection}, no eyes)`);
  } catch (error) {
    console.error('🚨 Error drawing pose keypoints:', error);
  }
};

// Helper function to calculate angle between three points
export const calculateAngle = (
  point1: PoseKeypoint,
  point2: PoseKeypoint, // vertex
  point3: PoseKeypoint
): number => {
  const vector1 = { x: point1.x - point2.x, y: point1.y - point2.y };
  const vector2 = { x: point3.x - point2.x, y: point3.y - point2.y };
  
  const dot = vector1.x * vector2.x + vector1.y * vector2.y;
  const mag1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
  const mag2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);
  
  const cosAngle = dot / (mag1 * mag2);
  const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
  
  return (angle * 180) / Math.PI;
};

// REFINED: Primary arm elbow angle only - positioned on inside of elbow
export const drawElbowAngles = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[]
) => {
  try {
    if (!keypoints || keypoints.length < 17) return;

    // DETECT POSE DIRECTION: Determine which side is primary
    const poseDirection = detectPoseDirection(keypoints);

    // Left elbow angle (shoulder -> elbow -> wrist)
    const leftShoulder = keypoints[5];
    const leftElbow = keypoints[7];
    const leftWrist = keypoints[9];

    // Right elbow angle (shoulder -> elbow -> wrist)
    const rightShoulder = keypoints[6];
    const rightElbow = keypoints[8];
    const rightWrist = keypoints[10];

    // REFINED: Only draw primary arm elbow angle
    if (poseDirection === 'left-facing') {
      // Left-facing: only show left elbow angle
      if (leftShoulder && leftElbow && leftWrist &&
          leftShoulder.score > 0.3 && leftElbow.score > 0.3 && leftWrist.score > 0.3) {
        const leftAngle = calculateAngle(leftShoulder, leftElbow, leftWrist);
        drawAngleArcInside(ctx, leftShoulder, leftElbow, leftWrist, 20, leftAngle, 1.0);
      }
    } else if (poseDirection === 'right-facing') {
      // Right-facing: only show right elbow angle
      if (rightShoulder && rightElbow && rightWrist &&
          rightShoulder.score > 0.3 && rightElbow.score > 0.3 && rightWrist.score > 0.3) {
        const rightAngle = calculateAngle(rightShoulder, rightElbow, rightWrist);
        drawAngleArcInside(ctx, rightShoulder, rightElbow, rightWrist, 20, rightAngle, 1.0);
      }
    } else {
      // Unknown direction: default to left elbow only
      if (leftShoulder && leftElbow && leftWrist &&
          leftShoulder.score > 0.3 && leftElbow.score > 0.3 && leftWrist.score > 0.3) {
        const leftAngle = calculateAngle(leftShoulder, leftElbow, leftWrist);
        drawAngleArcInside(ctx, leftShoulder, leftElbow, leftWrist, 20, leftAngle, 1.0);
      }
    }

    console.log(`📐 PRIMARY ELBOW ANGLE: Single arm rendering (${poseDirection})`);
  } catch (error) {
    console.error('🚨 Error drawing elbow angles:', error);
  }
};

// REFINED: Draw angle arc on the inside of the elbow joint
const drawAngleArcInside = (
  ctx: CanvasRenderingContext2D,
  point1: PoseKeypoint,
  vertex: PoseKeypoint,
  point3: PoseKeypoint,
  radius: number = 20,
  angleDegrees: number,
  opacity: number = 1.0
) => {
  // Calculate vectors from vertex to both points
  const vec1 = { x: point1.x - vertex.x, y: point1.y - vertex.y };
  const vec2 = { x: point3.x - vertex.x, y: point3.y - vertex.y };

  // Normalize vectors
  const mag1 = Math.sqrt(vec1.x * vec1.x + vec1.y * vec1.y);
  const mag2 = Math.sqrt(vec2.x * vec2.x + vec2.y * vec2.y);

  if (mag1 === 0 || mag2 === 0) return;

  vec1.x /= mag1; vec1.y /= mag1;
  vec2.x /= mag2; vec2.y /= mag2;

  // Calculate angles
  const angle1 = Math.atan2(vec1.y, vec1.x);
  const angle2 = Math.atan2(vec2.y, vec2.x);

  // Determine the smaller arc (inside the elbow)
  let startAngle = angle1;
  let endAngle = angle2;

  // Ensure we draw the smaller arc (inside the joint)
  let angleDiff = endAngle - startAngle;
  if (angleDiff > Math.PI) {
    angleDiff -= 2 * Math.PI;
  } else if (angleDiff < -Math.PI) {
    angleDiff += 2 * Math.PI;
  }

  // If the angle is obtuse, we want the smaller arc
  if (Math.abs(angleDiff) > Math.PI / 2) {
    if (angleDiff > 0) {
      endAngle = startAngle + (2 * Math.PI - angleDiff);
    } else {
      endAngle = startAngle + (2 * Math.PI + angleDiff);
    }
  }

  ctx.globalAlpha = opacity;

  // Draw solid orange arc on the inside of the elbow
  ctx.setLineDash([]);
  ctx.beginPath();
  ctx.arc(vertex.x, vertex.y, radius, startAngle, endAngle);
  ctx.strokeStyle = '#FFA500'; // Solid orange for elbow angles
  ctx.lineWidth = 3;
  ctx.stroke();

  // Draw angle text positioned inside the arc
  const midAngle = (startAngle + endAngle) / 2;
  const textX = vertex.x + Math.cos(midAngle) * (radius - 10); // Inside the arc
  const textY = vertex.y + Math.sin(midAngle) * (radius - 10);

  ctx.fillStyle = '#FFA500'; // Orange text to match arc
  ctx.font = 'bold 11px Arial';
  ctx.textAlign = 'center';
  ctx.fillText(`${Math.round(angleDegrees)}°`, textX, textY);

  ctx.globalAlpha = 1.0;
};

// REFINED: Enhanced posture analysis with spine alignment and head angle
export const drawPostureAnalysis = (
  ctx: CanvasRenderingContext2D,
  keypoints: PoseKeypoint[],
  _canvasWidth: number,
  canvasHeight: number
) => {
  const nose = keypoints[0];
  const leftEar = keypoints[3];
  const rightEar = keypoints[4];
  const leftHip = keypoints[11];
  const rightHip = keypoints[12];

  if (!nose || !leftHip || !rightHip ||
      nose.score < 0.3 || leftHip.score < 0.3 || rightHip.score < 0.3) {
    return;
  }

  // Calculate hip center
  const hipCenterX = (leftHip.x + rightHip.x) / 2;
  const hipCenterY = (leftHip.y + rightHip.y) / 2;

  // Calculate ear center (if ears are detected)
  let earCenterX = nose.x; // Fallback to nose
  let earCenterY = nose.y - 20; // Estimate ear level above nose
  let hasEarData = false;

  if (leftEar && rightEar && leftEar.score > 0.3 && rightEar.score > 0.3) {
    earCenterX = (leftEar.x + rightEar.x) / 2;
    earCenterY = (leftEar.y + rightEar.y) / 2;
    hasEarData = true;
  } else if (leftEar && leftEar.score > 0.3) {
    earCenterX = leftEar.x;
    earCenterY = leftEar.y;
    hasEarData = true;
  } else if (rightEar && rightEar.score > 0.3) {
    earCenterX = rightEar.x;
    earCenterY = rightEar.y;
    hasEarData = true;
  }

  // SPINE ALIGNMENT: Calculate lean angle from hips to ear center
  const spineX = earCenterX - hipCenterX;
  const spineY = earCenterY - hipCenterY;
  const spineLeanAngle = Math.atan2(spineX, spineY) * (180 / Math.PI);

  // HEAD ANGLE: Calculate head angle from ear center to nose
  const headX = nose.x - earCenterX;
  const headY = nose.y - earCenterY;
  const headAngle = Math.atan2(headX, headY) * (180 / Math.PI);

  // Determine posture status based on spine alignment
  let postureStatus = 'NEUTRAL';
  let postureColor = '#00FF41'; // Green for good posture

  if (Math.abs(spineLeanAngle) > 15) {
    postureStatus = spineLeanAngle > 0 ? 'FORWARD LEAN' : 'BACKWARD LEAN';
    postureColor = '#FF6B6B'; // Red for problematic posture
  } else if (Math.abs(spineLeanAngle) > 8) {
    postureStatus = 'SLIGHT LEAN';
    postureColor = '#FFD700'; // Yellow for minor issues
  }

  // Draw spine reference line (hips to ear center)
  ctx.strokeStyle = postureColor;
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]); // Red dotted line as requested
  ctx.beginPath();
  ctx.moveTo(hipCenterX, hipCenterY);
  ctx.lineTo(earCenterX, earCenterY);
  ctx.stroke();

  // Draw head angle line (ear center to nose) - lighter color
  ctx.strokeStyle = '#40E0D0'; // Turquoise for head angle
  ctx.lineWidth = 1.5;
  ctx.setLineDash([3, 3]);
  ctx.beginPath();
  ctx.moveTo(earCenterX, earCenterY);
  ctx.lineTo(nose.x, nose.y);
  ctx.stroke();
  ctx.setLineDash([]);

  // Draw enhanced posture indicator
  ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
  ctx.fillRect(10, canvasHeight - 100, 220, 90);

  ctx.fillStyle = postureColor;
  ctx.font = 'bold 13px Arial';
  ctx.textAlign = 'left';
  ctx.fillText('SPINE POSTURE:', 15, canvasHeight - 80);
  ctx.fillText(postureStatus, 15, canvasHeight - 60);
  ctx.fillText(`${Math.abs(spineLeanAngle).toFixed(1)}° from vertical`, 15, canvasHeight - 40);

  // Add head angle information
  ctx.fillStyle = '#40E0D0';
  ctx.font = '11px Arial';
  ctx.fillText(`Head: ${Math.abs(headAngle).toFixed(1)}° ${hasEarData ? '(ears detected)' : '(estimated)'}`, 15, canvasHeight - 20);
};

// DEBUG FRAME: Simple frame counter for development
export const drawDebugFrame = (
  ctx: CanvasRenderingContext2D,
  frameCount: number
) => {
  // Medical green theme for debug info
  ctx.fillStyle = 'rgba(0, 255, 65, 0.9)'; // Medical green
  ctx.fillRect(10, 10, 150, 30);
  ctx.fillStyle = 'white';
  ctx.font = 'bold 14px Arial';
  ctx.fillText(`FRAME ${frameCount}`, 15, 30);
};
