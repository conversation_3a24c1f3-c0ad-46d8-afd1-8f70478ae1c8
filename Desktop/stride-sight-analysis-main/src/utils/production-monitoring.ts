/**
 * SECTION 3: Production Monitoring and Telemetry System
 * Implements comprehensive monitoring, error reporting, and user feedback collection
 */

import * as tf from '@tensorflow/tfjs-core';

export interface ProductionMetrics {
  sessionId: string;
  timestamp: number;
  pipelineMode: '2D' | '3D' | 'fallback';
  performance: {
    processingTime: number;
    framesPerSecond: number;
    memoryUsage: number;
    tensorCount: number;
  };
  system: {
    userAgent: string;
    webglSupport: boolean;
    memoryInfo?: any;
    screenResolution: string;
  };
  errors?: ProductionError[];
  userFeedback?: UserFeedback;
}

export interface ProductionError {
  id: string;
  timestamp: number;
  type: 'pipeline' | 'memory' | 'detection' | 'processing' | 'system';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  stack?: string;
  context: {
    pipelineMode: string;
    memoryInfo: tf.MemoryInfo;
    userAgent: string;
    url: string;
  };
  recovery?: {
    attempted: boolean;
    successful: boolean;
    method: string;
  };
}

export interface UserFeedback {
  sessionId: string;
  timestamp: number;
  pipelineMode: '2D' | '3D';
  rating: number; // 1-5 scale
  performance: 'excellent' | 'good' | 'fair' | 'poor';
  issues: string[];
  suggestions: string;
  wouldRecommend: boolean;
}

export interface SystemCapabilityReport {
  webglSupport: boolean;
  webglVersion: string;
  memoryAvailable: number;
  cpuCores: number;
  performanceScore: number;
  recommendedMode: '2D' | '3D';
  limitations: string[];
}

export class ProductionMonitoring {
  private sessionId: string;
  private metrics: ProductionMetrics;
  private errors: ProductionError[] = [];
  private isEnabled: boolean = true;
  private reportingEndpoint?: string;

  constructor(config?: { reportingEndpoint?: string; enabled?: boolean }) {
    this.sessionId = this.generateSessionId();
    this.isEnabled = config?.enabled ?? true;
    this.reportingEndpoint = config?.reportingEndpoint;
    
    this.metrics = {
      sessionId: this.sessionId,
      timestamp: Date.now(),
      pipelineMode: '2D',
      performance: {
        processingTime: 0,
        framesPerSecond: 0,
        memoryUsage: 0,
        tensorCount: 0
      },
      system: {
        userAgent: navigator.userAgent,
        webglSupport: this.checkWebGLSupport(),
        memoryInfo: (performance as any).memory,
        screenResolution: `${screen.width}x${screen.height}`
      }
    };

    console.log('🔍 SECTION 3: Production monitoring initialized');
    console.log('🔍 SECTION 3: Session ID:', this.sessionId);
  }

  /**
   * SECTION 3: Track performance metrics
   */
  trackPerformance(metrics: {
    processingTime: number;
    framesPerSecond: number;
    memoryUsage: number;
    tensorCount: number;
    pipelineMode: '2D' | '3D' | 'fallback';
  }): void {
    if (!this.isEnabled) return;

    this.metrics.performance = {
      processingTime: metrics.processingTime,
      framesPerSecond: metrics.framesPerSecond,
      memoryUsage: metrics.memoryUsage,
      tensorCount: metrics.tensorCount
    };
    this.metrics.pipelineMode = metrics.pipelineMode;
    this.metrics.timestamp = Date.now();

    // Log performance warnings
    if (metrics.processingTime > 100) {
      console.warn('🔍 SECTION 3: High processing time detected:', metrics.processingTime + 'ms');
    }
    
    if (metrics.memoryUsage > 500) {
      console.warn('🔍 SECTION 3: High memory usage detected:', metrics.memoryUsage + 'MB');
    }

    // Send metrics periodically
    this.sendMetricsIfNeeded();
  }

  /**
   * SECTION 3: Report production errors
   */
  reportError(error: Error, context: {
    type: ProductionError['type'];
    severity: ProductionError['severity'];
    pipelineMode: string;
    recovery?: ProductionError['recovery'];
  }): string {
    if (!this.isEnabled) return '';

    const errorId = this.generateErrorId();
    const productionError: ProductionError = {
      id: errorId,
      timestamp: Date.now(),
      type: context.type,
      severity: context.severity,
      message: error.message,
      stack: error.stack,
      context: {
        pipelineMode: context.pipelineMode,
        memoryInfo: tf.memory(),
        userAgent: navigator.userAgent,
        url: window.location.href
      },
      recovery: context.recovery
    };

    this.errors.push(productionError);
    
    console.error('🔍 SECTION 3: Production error reported:', {
      id: errorId,
      type: context.type,
      severity: context.severity,
      message: error.message
    });

    // Send critical errors immediately
    if (context.severity === 'critical') {
      this.sendErrorReport(productionError);
    }

    return errorId;
  }

  /**
   * SECTION 3: Collect user feedback
   */
  collectUserFeedback(feedback: Omit<UserFeedback, 'sessionId' | 'timestamp'>): void {
    if (!this.isEnabled) return;

    const userFeedback: UserFeedback = {
      sessionId: this.sessionId,
      timestamp: Date.now(),
      ...feedback
    };

    this.metrics.userFeedback = userFeedback;

    console.log('🔍 SECTION 3: User feedback collected:', {
      rating: feedback.rating,
      performance: feedback.performance,
      pipelineMode: feedback.pipelineMode
    });

    this.sendUserFeedback(userFeedback);
  }

  /**
   * SECTION 3: Generate system capability report
   */
  async generateSystemCapabilityReport(): Promise<SystemCapabilityReport> {
    console.log('🔍 SECTION 3: Generating system capability report');

    const webglSupport = this.checkWebGLSupport();
    const webglVersion = this.getWebGLVersion();
    const memoryInfo = (performance as any).memory;
    const memoryAvailable = memoryInfo ? memoryInfo.totalJSHeapSize / 1024 / 1024 : 0;
    const cpuCores = navigator.hardwareConcurrency || 1;
    
    // Calculate performance score
    let performanceScore = 0;
    if (webglSupport) performanceScore += 30;
    if (memoryAvailable > 1000) performanceScore += 25; // > 1GB
    if (cpuCores >= 4) performanceScore += 20;
    if (webglVersion.includes('2.0')) performanceScore += 25;

    // Determine recommended mode
    const recommendedMode: '2D' | '3D' = performanceScore >= 70 ? '3D' : '2D';

    // Identify limitations
    const limitations: string[] = [];
    if (!webglSupport) limitations.push('WebGL not supported');
    if (memoryAvailable < 1000) limitations.push('Limited memory available');
    if (cpuCores < 2) limitations.push('Limited CPU cores');
    if (!webglVersion.includes('2.0')) limitations.push('WebGL 2.0 not supported');

    const report: SystemCapabilityReport = {
      webglSupport,
      webglVersion,
      memoryAvailable,
      cpuCores,
      performanceScore,
      recommendedMode,
      limitations
    };

    console.log('🔍 SECTION 3: System capability report:', report);
    return report;
  }

  /**
   * SECTION 3: Track pipeline switching events
   */
  trackPipelineSwitch(from: '2D' | '3D', to: '2D' | '3D', reason: string): void {
    if (!this.isEnabled) return;

    console.log('🔍 SECTION 3: Pipeline switch tracked:', { from, to, reason });

    // Track switch in metrics
    this.metrics.pipelineMode = to;
    this.metrics.timestamp = Date.now();

    // Send switch event
    this.sendEvent('pipeline_switch', {
      from,
      to,
      reason,
      sessionId: this.sessionId,
      timestamp: Date.now()
    });
  }

  /**
   * SECTION 3: Track user engagement
   */
  trackUserEngagement(event: string, data?: any): void {
    if (!this.isEnabled) return;

    console.log('🔍 SECTION 3: User engagement tracked:', event, data);

    this.sendEvent('user_engagement', {
      event,
      data,
      sessionId: this.sessionId,
      timestamp: Date.now(),
      pipelineMode: this.metrics.pipelineMode
    });
  }

  /**
   * SECTION 3: Get current metrics
   */
  getCurrentMetrics(): ProductionMetrics {
    return { ...this.metrics };
  }

  /**
   * SECTION 3: Get error summary
   */
  getErrorSummary(): {
    totalErrors: number;
    errorsByType: Record<string, number>;
    errorsBySeverity: Record<string, number>;
    recentErrors: ProductionError[];
  } {
    const errorsByType: Record<string, number> = {};
    const errorsBySeverity: Record<string, number> = {};

    this.errors.forEach(error => {
      errorsByType[error.type] = (errorsByType[error.type] || 0) + 1;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + 1;
    });

    return {
      totalErrors: this.errors.length,
      errorsByType,
      errorsBySeverity,
      recentErrors: this.errors.slice(-10) // Last 10 errors
    };
  }

  /**
   * SECTION 3: Enable/disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
    console.log('🔍 SECTION 3: Monitoring', enabled ? 'enabled' : 'disabled');
  }

  // Private helper methods

  private generateSessionId(): string {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private generateErrorId(): string {
    return 'error_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  private checkWebGLSupport(): boolean {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
      return !!gl;
    } catch {
      return false;
    }
  }

  private getWebGLVersion(): string {
    try {
      const canvas = document.createElement('canvas');
      const gl2 = canvas.getContext('webgl2');
      if (gl2) return 'WebGL 2.0';
      
      const gl = canvas.getContext('webgl');
      if (gl) return 'WebGL 1.0';
      
      return 'Not supported';
    } catch {
      return 'Not supported';
    }
  }

  private sendMetricsIfNeeded(): void {
    // Send metrics every 30 seconds
    const now = Date.now();
    if (now - this.metrics.timestamp > 30000) {
      this.sendMetrics();
    }
  }

  private async sendMetrics(): Promise<void> {
    if (!this.reportingEndpoint) return;

    try {
      await fetch(this.reportingEndpoint + '/metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(this.metrics)
      });
    } catch (error) {
      console.warn('🔍 SECTION 3: Failed to send metrics:', error);
    }
  }

  private async sendErrorReport(error: ProductionError): Promise<void> {
    if (!this.reportingEndpoint) return;

    try {
      await fetch(this.reportingEndpoint + '/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(error)
      });
    } catch (sendError) {
      console.warn('🔍 SECTION 3: Failed to send error report:', sendError);
    }
  }

  private async sendUserFeedback(feedback: UserFeedback): Promise<void> {
    if (!this.reportingEndpoint) return;

    try {
      await fetch(this.reportingEndpoint + '/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(feedback)
      });
    } catch (error) {
      console.warn('🔍 SECTION 3: Failed to send user feedback:', error);
    }
  }

  private async sendEvent(eventType: string, eventData: any): Promise<void> {
    if (!this.reportingEndpoint) return;

    try {
      await fetch(this.reportingEndpoint + '/events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: eventType, data: eventData })
      });
    } catch (error) {
      console.warn('🔍 SECTION 3: Failed to send event:', error);
    }
  }
}

// Export singleton instance for global use
export const productionMonitoring = new ProductionMonitoring({
  enabled: process.env.NODE_ENV === 'production',
  reportingEndpoint: process.env.REACT_APP_MONITORING_ENDPOINT
});
