
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from '../calculators/interfaces/common_interfaces';

export interface VisibilitySmoothingConfig {
  alpha: number;
  threshold?: number;
  adaptiveMode?: boolean;
}

/**
 * PHASE 4: Enhanced Low-pass filter for smoothing visibility scores with adaptive thresholding.
 */
export class LowPassVisibilityFilter {
  private readonly config: VisibilitySmoothingConfig;
  private previousScores?: number[];
  private scoreHistory: number[][] = [];
  private readonly maxHistoryLength = 10;
  private callCount: number = 0;

  constructor(alpha: number, threshold?: number, adaptiveMode: boolean = false) {
    this.config = { 
      alpha, 
      threshold: threshold || 0.3, 
      adaptiveMode 
    };
  }

  apply(score: number): number;
  apply(keypoints: Keypoint[]): Keypoint[];
  apply(input: number | Keypoint[]): number | Keypoint[] {
    if (typeof input === 'number') {
      return this.applySingleScore(input);
    }
    return this.applyKeypointsArray(input);
  }

  private applySingleScore(score: number): number {
    if (this.previousScores === undefined) {
      this.previousScores = [score];
      return score;
    }

    const previousScore = this.previousScores[0] || 0;
    let alpha = this.config.alpha;

    // PHASE 4: Adaptive alpha based on score confidence
    if (this.config.adaptiveMode && score < this.config.threshold!) {
      alpha = Math.max(0.1, alpha * 0.5); // Reduce smoothing for low confidence scores
    }

    const smoothedScore = alpha * score + (1 - alpha) * previousScore;
    this.previousScores[0] = smoothedScore;

    return smoothedScore;
  }

  private applyKeypointsArray(keypoints: Keypoint[]): Keypoint[] {
    this.callCount++;
    if (this.callCount % 60 === 0) {
      console.log('🔧 PHASE 4 VISIBILITY SMOOTHING: Applying enhanced visibility smoothing with adaptive thresholding');
      console.log('🔧 PHASE 4 VISIBILITY SMOOTHING: Input keypoints count:', keypoints.length);
    }

    if (!this.previousScores) {
      this.previousScores = keypoints.map(kp => kp.score || 0);
      this.scoreHistory.push(this.previousScores.slice());
      return keypoints;
    }

    const currentScores = keypoints.map(kp => kp.score || 0);
    
    // PHASE 4: Track score history for better adaptive filtering
    this.scoreHistory.push(currentScores.slice());
    if (this.scoreHistory.length > this.maxHistoryLength) {
      this.scoreHistory.shift();
    }

    const smoothedKeypoints: Keypoint[] = keypoints.map((keypoint, index) => {
      const currentScore = keypoint.score || 0;
      const previousScore = this.previousScores![index] || 0;
      
      let alpha = this.config.alpha;

      // PHASE 4: Enhanced adaptive smoothing
      if (this.config.adaptiveMode) {
        // Calculate score variance over history
        const scoreVariance = this.calculateScoreVariance(index);
        
        // Adjust alpha based on confidence and variance
        if (currentScore < this.config.threshold! || scoreVariance > 0.1) {
          alpha = Math.max(0.05, alpha * 0.3); // Strong smoothing for unstable scores
        } else if (currentScore > 0.8 && scoreVariance < 0.05) {
          alpha = Math.min(0.9, alpha * 1.5); // Lighter smoothing for stable high-confidence scores
        }
      }

      const smoothedScore = alpha * currentScore + (1 - alpha) * previousScore;
      this.previousScores![index] = smoothedScore;

      return {
        ...keypoint,
        score: smoothedScore
      };
    });

    if (this.callCount % 60 === 0) {
      console.log(`🔧 PHASE 4 VISIBILITY SMOOTHING: Successfully smoothed ${smoothedKeypoints.length} visibility scores with adaptive filtering`);
    }
    return smoothedKeypoints;
  }

  private calculateScoreVariance(keypointIndex: number): number {
    if (this.scoreHistory.length < 3) return 0;

    const scores = this.scoreHistory.map(frame => frame[keypointIndex] || 0);
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    
    return variance;
  }

  reset(): void {
    console.log('🔧 PHASE 4 VISIBILITY SMOOTHING: Resetting enhanced filter state');
    this.previousScores = undefined;
    this.scoreHistory = [];
  }
}
