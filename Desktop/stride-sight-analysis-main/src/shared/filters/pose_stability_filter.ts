
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from '../calculators/interfaces/common_interfaces';

export interface PoseStabilityConfig {
  stabilityThreshold: number;
  minStableFrames: number;
  maxInstabilityFrames: number;
  positionTolerance: number;
}

/**
 * PHASE 4: Pose stability filter to detect and handle pose tracking instabilities.
 */
export class PoseStabilityFilter {
  private readonly config: PoseStabilityConfig;
  private poseHistory: Keypoint[][] = [];
  private stableFrameCount = 0;
  private unstableFrameCount = 0;
  private lastStablePose?: Keypoint[];
  private readonly maxHistoryLength = 30;
  private callCount: number = 0;

  constructor(config: Partial<PoseStabilityConfig> = {}) {
    this.config = {
      stabilityThreshold: config.stabilityThreshold || 0.7,
      minStableFrames: config.minStableFrames || 5,
      maxInstabilityFrames: config.maxInstabilityFrames || 10,
      positionTolerance: config.positionTolerance || 50.0,
      ...config
    };
  }

  apply(keypoints: Keypoint[]): { keypoints: Keypoint[]; isStable: boolean; confidence: number } {
    this.callCount++;
    if (this.callCount % 60 === 0) {
      console.log('🔧 PHASE 4 POSE STABILITY: Analyzing pose stability');
    }
    
    // Add current pose to history
    this.poseHistory.push([...keypoints]);
    if (this.poseHistory.length > this.maxHistoryLength) {
      this.poseHistory.shift();
    }

    // Calculate pose stability metrics
    const stability = this.calculatePoseStability(keypoints);
    const isCurrentlyStable = stability.confidence > this.config.stabilityThreshold;

    if (isCurrentlyStable) {
      this.stableFrameCount++;
      this.unstableFrameCount = 0;
      
      // Update last stable pose if we have enough stable frames
      if (this.stableFrameCount >= this.config.minStableFrames) {
        this.lastStablePose = [...keypoints];
      }
    } else {
      this.unstableFrameCount++;
      this.stableFrameCount = 0;
    }

    // Determine output pose
    let outputPose = keypoints;
    let outputStability = isCurrentlyStable;
    let outputConfidence = stability.confidence;

    // PHASE 4: Handle instability by reverting to last stable pose
    if (this.unstableFrameCount > this.config.maxInstabilityFrames && this.lastStablePose) {
      if (this.callCount % 60 === 0) {
        console.log('🔧 PHASE 4 POSE STABILITY: Pose unstable, reverting to last stable pose');
      }
      outputPose = [...this.lastStablePose];
      outputStability = false;
      outputConfidence = 0.5; // Reduced confidence for reverted pose
    }

    if (this.callCount % 60 === 0) {
      console.log(`🔧 PHASE 4 POSE STABILITY: Stability analysis complete - Stable: ${outputStability}, Confidence: ${outputConfidence.toFixed(3)}`);
    }

    return {
      keypoints: outputPose,
      isStable: outputStability,
      confidence: outputConfidence
    };
  }

  private calculatePoseStability(currentPose: Keypoint[]): { confidence: number; metrics: any } {
    if (this.poseHistory.length < 3) {
      return { confidence: 0.5, metrics: {} };
    }

    const recentPoses = this.poseHistory.slice(-5); // Last 5 frames
    let totalPositionVariance = 0;
    let totalScoreVariance = 0;
    let validKeypointCount = 0;

    // Calculate variance for each keypoint across recent frames
    for (let i = 0; i < currentPose.length; i++) {
      const positions = recentPoses.map(pose => pose[i]).filter(kp => kp && kp.score && kp.score > 0.1);
      
      if (positions.length < 2) continue;

      // Position variance
      const avgX = positions.reduce((sum, kp) => sum + kp.x, 0) / positions.length;
      const avgY = positions.reduce((sum, kp) => sum + kp.y, 0) / positions.length;
      const posVariance = positions.reduce((sum, kp) => 
        sum + Math.sqrt((kp.x - avgX) ** 2 + (kp.y - avgY) ** 2), 0) / positions.length;

      // Score variance
      const avgScore = positions.reduce((sum, kp) => sum + (kp.score || 0), 0) / positions.length;
      const scoreVariance = positions.reduce((sum, kp) => 
        sum + Math.abs((kp.score || 0) - avgScore), 0) / positions.length;

      totalPositionVariance += posVariance;
      totalScoreVariance += scoreVariance;
      validKeypointCount++;
    }

    if (validKeypointCount === 0) {
      return { confidence: 0.1, metrics: {} };
    }

    const avgPositionVariance = totalPositionVariance / validKeypointCount;
    const avgScoreVariance = totalScoreVariance / validKeypointCount;

    // Calculate stability confidence
    const positionStability = Math.max(0, 1 - (avgPositionVariance / this.config.positionTolerance));
    const scoreStability = Math.max(0, 1 - (avgScoreVariance * 2)); // Score variance is typically 0-0.5
    
    const overallConfidence = (positionStability * 0.7 + scoreStability * 0.3);

    const metrics = {
      avgPositionVariance,
      avgScoreVariance,
      positionStability,
      scoreStability,
      validKeypointCount,
      historyLength: this.poseHistory.length
    };

    return { confidence: overallConfidence, metrics };
  }

  reset(): void {
    console.log('🔧 PHASE 4 POSE STABILITY: Resetting stability filter');
    this.poseHistory = [];
    this.stableFrameCount = 0;
    this.unstableFrameCount = 0;
    this.lastStablePose = undefined;
  }

  getStabilityMetrics(): any {
    return {
      stableFrameCount: this.stableFrameCount,
      unstableFrameCount: this.unstableFrameCount,
      hasLastStablePose: !!this.lastStablePose,
      historyLength: this.poseHistory.length
    };
  }
}
