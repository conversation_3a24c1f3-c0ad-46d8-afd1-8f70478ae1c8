/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import * as poseDetection from '@tensorflow-models/pose-detection';
import { Keypoint, ImageSize } from './interfaces/common_interfaces';
import { tensorsToLandmarks, TensorsToLandmarksConfig } from './tensors_to_landmarks';
import { refineLandmarksFromHeatmap, RefineLandmarksFromHeatmapConfig } from './refine_landmarks_from_heatmap';
import { tensorsToDetections, TensorsToDetectionsConfig } from './tensors_to_detections';
import { detectorResult, DetectorResult } from './detector_result';
import { convertImageToTensor, preprocessImageForBlazePose } from './convert_image_to_tensor';
import { safeTensorDispose, validateTensor, filterNaNValues, filterNaNValues2D, ensureTensor2D, keypointsToTensor2D } from './tensor_utils';
import { validateAndCleanPoseCoordinates } from './blazepose_tensor_processor';
import { createSsdAnchors, SsdAnchorConfig, Anchor } from './create_ssd_anchors';
import { Detection } from './interfaces/common_interfaces';

/**
 * PHASE 4 COMPLETE: Custom BlazePose tensor processing pipeline
 * This implements the complete tensor processing pipeline as specified in BlazePose_Architecture.md
 * Bypasses the estimatePoses() method to use raw tensor processing
 */

export interface BlazePoseCustomConfig {
  modelType: 'lite' | 'full' | 'heavy';
  enableLandmarkRefinement: boolean;
  enableCoordinateFiltering: boolean;
  inputResolution: { width: number; height: number };
  scoreThreshold: number;
  maxPoses: number;
}

export interface CustomPoseResult {
  pose: poseDetection.Pose;
  processingMetrics: {
    detectionTime: number;
    refinementTime: number;
    totalTime: number;
    tensorCount: number;
  };
}

/**
 * Main custom BlazePose processor that implements the complete tensor pipeline
 */
export class BlazePoseCustomProcessor {
  private modelType: 'lite' | 'full' | 'heavy';
  private enableLandmarkRefinement: boolean;
  private enableCoordinateFiltering: boolean;
  private inputResolution: { width: number; height: number };
  private scoreThreshold: number;
  private maxPoses: number;

  constructor(config: BlazePoseCustomConfig) {
    this.modelType = config.modelType;
    this.enableLandmarkRefinement = config.enableLandmarkRefinement;
    this.enableCoordinateFiltering = config.enableCoordinateFiltering;
    this.inputResolution = config.inputResolution;
    this.scoreThreshold = config.scoreThreshold;
    this.maxPoses = config.maxPoses;

    console.log('🔧 BLAZEPOSE CUSTOM: Initialized custom processor with config:', config);
  }

  /**
   * Process raw model outputs through the complete tensor pipeline
   */
  async processModelOutputs(
    modelOutputs: tf.Tensor[],
    inputVideo: HTMLVideoElement,
    imageSize: ImageSize
  ): Promise<CustomPoseResult[]> {
    
    const startTime = performance.now();
    console.log('🔧 BLAZEPOSE CUSTOM: Starting complete tensor processing pipeline');
    console.log('🔧 BLAZEPOSE CUSTOM: Model outputs count:', modelOutputs.length);
    console.log('🔧 BLAZEPOSE CUSTOM: Input image size:', imageSize);

    try {
      // PHASE 1: Process detection outputs (bounding boxes and scores)
      const detectionStartTime = performance.now();
      const detectionResults = await this.processDetectionPhase(modelOutputs, imageSize);
      const detectionTime = performance.now() - detectionStartTime;

      console.log('🔧 BLAZEPOSE CUSTOM: Detection phase complete:', {
        time: detectionTime.toFixed(2) + 'ms',
        detectionsFound: detectionResults.length
      });

      if (detectionResults.length === 0) {
        console.log('⚠️ BLAZEPOSE CUSTOM: No poses detected in detection phase');
        return [];
      }

      // PHASE 2: Process landmark outputs for each detection
      const refinementStartTime = performance.now();
      const customPoseResults: CustomPoseResult[] = [];

      for (let i = 0; i < Math.min(detectionResults.length, this.maxPoses); i++) {
        const detection = detectionResults[i];
        
        console.log(`🔧 BLAZEPOSE CUSTOM: Processing pose ${i + 1}/${detectionResults.length}`);
        
        const poseResult = await this.processLandmarkPhase(
          modelOutputs,
          detection,
          inputVideo,
          imageSize
        );

        if (poseResult) {
          const refinementTime = performance.now() - refinementStartTime;
          const totalTime = performance.now() - startTime;

          customPoseResults.push({
            pose: poseResult,
            processingMetrics: {
              detectionTime,
              refinementTime,
              totalTime,
              tensorCount: modelOutputs.length
            }
          });
        }
      }

      const totalTime = performance.now() - startTime;
      console.log('✅ BLAZEPOSE CUSTOM: Complete pipeline finished:', {
        totalTime: totalTime.toFixed(2) + 'ms',
        posesProcessed: customPoseResults.length,
        averageTimePerPose: customPoseResults.length > 0 ? (totalTime / customPoseResults.length).toFixed(2) + 'ms' : '0ms'
      });

      return customPoseResults;

    } catch (error) {
      console.error('❌ BLAZEPOSE CUSTOM: Pipeline processing error:', error);
      return [];
    } finally {
      // Clean up any temporary tensors
      this.cleanupTensors(modelOutputs);
    }
  }

  /**
   * PHASE 1: Process detection tensors to find pose bounding boxes
   */
  private async processDetectionPhase(
    modelOutputs: tf.Tensor[],
    imageSize: ImageSize
  ): Promise<Detection[]> {
    
    console.log('🔧 BLAZEPOSE CUSTOM: PHASE 1 - Processing detection tensors');

    try {
      // Extract detection tensors (typically first 2 tensors)
      const detectionTensors = modelOutputs.slice(0, 2);
      
      if (detectionTensors.length < 2) {
        console.warn('🔧 BLAZEPOSE CUSTOM: Insufficient detection tensors');
        return [];
      }

      // Create SSD anchors for BlazePose detection
      const anchorConfig: SsdAnchorConfig = {
        inputSizeWidth: this.inputResolution.width,
        inputSizeHeight: this.inputResolution.height,
        minScale: 0.1484375,
        maxScale: 0.75,
        anchorOffsetX: 0.5,
        anchorOffsetY: 0.5,
        numLayers: 4,
        featureMapWidth: [48, 24, 12, 6],
        featureMapHeight: [48, 24, 12, 6],
        strides: [4, 8, 16, 32],
        aspectRatios: [1.0],
        reduceBoxesInLowestLayer: false,
        interpolatedScaleAspectRatio: 1.0,
        fixedAnchorSize: true
      };

      const anchors = createSsdAnchors(anchorConfig);

      // Configure tensors to detections
      const detectionsConfig: TensorsToDetectionsConfig = {
        numClasses: 1,
        numBoxes: anchors.length,
        numCoords: 4,
        boxCoordOffset: 0,
        keypointCoordOffset: 4,
        numKeypoints: 0,
        numValuesPerKeypoint: 0,
        sigmoidScore: true,
        scoreClippingThresh: 100.0,
        reverseOutputOrder: false,
        xScale: this.inputResolution.width,
        yScale: this.inputResolution.height,
        wScale: this.inputResolution.width,
        hScale: this.inputResolution.height,
        minScoreThresh: this.scoreThreshold
      };

      // Convert tensors to detections
      const detections = await tensorsToDetections(
        detectionTensors,
        anchors,
        detectionsConfig
      );

      console.log('✅ BLAZEPOSE CUSTOM: PHASE 1 complete:', {
        detectionsCount: detections.length,
        scoreThreshold: this.scoreThreshold
      });

      return detections;

    } catch (error) {
      console.error('❌ BLAZEPOSE CUSTOM: PHASE 1 error:', error);
      return [];
    }
  }

  /**
   * PHASE 2: Process landmark tensors for a specific detection
   */
  private async processLandmarkPhase(
    modelOutputs: tf.Tensor[],
    detection: Detection,
    inputVideo: HTMLVideoElement,
    imageSize: ImageSize
  ): Promise<poseDetection.Pose | null> {
    
    console.log('🔧 BLAZEPOSE CUSTOM: PHASE 2 - Processing landmark tensors');

    try {
      // Extract landmark tensors (typically tensors 2+ are landmarks)
      const landmarkTensors = modelOutputs.slice(2);
      
      if (landmarkTensors.length === 0) {
        console.warn('🔧 BLAZEPOSE CUSTOM: No landmark tensors found');
        return null;
      }

      // Process 2D landmarks
      const landmarks2D = await this.processLandmarkTensor(
        landmarkTensors[0],
        imageSize,
        false // 2D processing
      );

      // Process 3D world landmarks if available
      let landmarks3D: Keypoint[] = [];
      if (landmarkTensors.length > 1) {
        landmarks3D = await this.processLandmarkTensor(
          landmarkTensors[1],
          imageSize,
          true // 3D processing
        );
      }

      // PHASE 3: Apply coordinate filtering and validation
      let processedLandmarks2D = landmarks2D;
      let processedLandmarks3D = landmarks3D;

      if (this.enableCoordinateFiltering) {
        console.log('🔧 BLAZEPOSE CUSTOM: PHASE 3 - Applying coordinate filtering');
        
        const cleanedResults = validateAndCleanPoseCoordinates(landmarks2D, landmarks3D);
        processedLandmarks2D = cleanedResults.landmarks;
        processedLandmarks3D = cleanedResults.worldLandmarks;
      }

      // PHASE 4: Apply landmark refinement if enabled
      if (this.enableLandmarkRefinement && landmarkTensors.length > 2) {
        console.log('🔧 BLAZEPOSE CUSTOM: PHASE 4 - Applying landmark refinement');
        
        processedLandmarks2D = await this.refineLandmarks(
          processedLandmarks2D,
          landmarkTensors[2] // Heatmap tensor
        );
      }

      // Create the final pose object
      const pose: poseDetection.Pose = {
        keypoints: processedLandmarks2D,
        keypoints3D: processedLandmarks3D.length > 0 ? processedLandmarks3D : undefined,
        score: detection.score || 0.5
      };

      console.log('✅ BLAZEPOSE CUSTOM: PHASE 2 complete:', {
        keypoints2D: processedLandmarks2D.length,
        keypoints3D: processedLandmarks3D.length,
        score: pose.score
      });

      return pose;

    } catch (error) {
      console.error('❌ BLAZEPOSE CUSTOM: PHASE 2 error:', error);
      return null;
    }
  }

  /**
   * Process individual landmark tensor (2D or 3D)
   */
  private async processLandmarkTensor(
    landmarkTensor: tf.Tensor,
    imageSize: ImageSize,
    is3D: boolean = false
  ): Promise<Keypoint[]> {
    
    console.log(`🔧 BLAZEPOSE CUSTOM: Processing ${is3D ? '3D' : '2D'} landmark tensor`);
    console.log('🔧 BLAZEPOSE CUSTOM: Tensor shape:', landmarkTensor.shape);

    try {
      // Validate tensor
      if (!validateTensor(landmarkTensor)) {
        console.warn('🔧 BLAZEPOSE CUSTOM: Invalid landmark tensor');
        return [];
      }

      // Filter NaN values
      const filteredTensor = filterNaNValues(landmarkTensor);

      // Configure tensorsToLandmarks
      const config: TensorsToLandmarksConfig = {
        numLandmarks: 33, // BlazePose has 33 landmarks
        inputImageWidth: imageSize.width,
        inputImageHeight: imageSize.height,
        normalizeZ: is3D ? 1.0 : undefined,
        visibilityActivation: 'sigmoid',
        flipHorizontally: false,
        flipVertically: false
      };

      // Convert tensor to landmarks
      const landmarks = await tensorsToLandmarks(
        filteredTensor as tf.Tensor2D,
        config
      );

      // Add BlazePose landmark names
      const namedLandmarks = this.addLandmarkNames(landmarks);

      console.log(`✅ BLAZEPOSE CUSTOM: Processed ${namedLandmarks.length} ${is3D ? '3D' : '2D'} landmarks`);

      // Clean up filtered tensor
      if (filteredTensor !== landmarkTensor) {
        safeTensorDispose(filteredTensor);
      }

      return namedLandmarks;

    } catch (error) {
      console.error(`❌ BLAZEPOSE CUSTOM: Error processing ${is3D ? '3D' : '2D'} landmarks:`, error);
      return [];
    }
  }

  /**
   * Apply landmark refinement using heatmap data
   */
  private async refineLandmarks(
    landmarks: Keypoint[],
    heatmapTensor: tf.Tensor
  ): Promise<Keypoint[]> {
    
    console.log('🔧 BLAZEPOSE CUSTOM: Applying landmark refinement');

    try {
      const config: RefineLandmarksFromHeatmapConfig = {
        kernelSize: 7,
        minConfidenceToRefine: 0.5
      };

      const refinedLandmarks = await refineLandmarksFromHeatmap(
        landmarks,
        heatmapTensor as tf.Tensor4D,
        config
      );

      console.log('✅ BLAZEPOSE CUSTOM: Landmark refinement complete:', {
        originalCount: landmarks.length,
        refinedCount: refinedLandmarks.length
      });

      return refinedLandmarks;

    } catch (error) {
      console.error('❌ BLAZEPOSE CUSTOM: Landmark refinement error:', error);
      return landmarks; // Return original landmarks on error
    }
  }

  /**
   * Add standard BlazePose landmark names
   */
  private addLandmarkNames(landmarks: Keypoint[]): Keypoint[] {
    const blazePoseLandmarkNames = [
      'nose',
      'left_eye_inner', 'left_eye', 'left_eye_outer',
      'right_eye_inner', 'right_eye', 'right_eye_outer',
      'left_ear', 'right_ear',
      'mouth_left', 'mouth_right',
      'left_shoulder', 'right_shoulder',
      'left_elbow', 'right_elbow',
      'left_wrist', 'right_wrist',
      'left_pinky', 'right_pinky',
      'left_index', 'right_index',
      'left_thumb', 'right_thumb',
      'left_hip', 'right_hip',
      'left_knee', 'right_knee',
      'left_ankle', 'right_ankle',
      'left_heel', 'right_heel',
      'left_foot_index', 'right_foot_index'
    ];

    return landmarks.map((landmark, index) => ({
      ...landmark,
      name: blazePoseLandmarkNames[index] || `landmark_${index}`
    }));
  }

  /**
   * Clean up tensors to prevent memory leaks
   */
  private cleanupTensors(tensors: tf.Tensor[]): void {
    console.log('🔧 BLAZEPOSE CUSTOM: Cleaning up tensors');
    
    try {
      tensors.forEach((tensor, index) => {
        if (tensor && typeof tensor.dispose === 'function') {
          tensor.dispose();
          console.log(`🔧 BLAZEPOSE CUSTOM: Disposed tensor ${index}`);
        }
      });
    } catch (error) {
      console.warn('🔧 BLAZEPOSE CUSTOM: Error during tensor cleanup:', error);
    }
  }
}

/**
 * Factory function to create a configured BlazePose custom processor
 */
export function createBlazePoseCustomProcessor(
  modelType: 'lite' | 'full' | 'heavy' = 'full',
  videoSetup?: string
): BlazePoseCustomProcessor {
  
  console.log('🔧 BLAZEPOSE CUSTOM: Creating custom processor for model:', modelType);
  
  // Determine optimal configuration based on model type and video setup
  const isBackFacing = videoSetup?.toLowerCase().includes('out') || 
                      videoSetup?.toLowerCase().includes('offset');

  const config: BlazePoseCustomConfig = {
    modelType: isBackFacing ? 'lite' : modelType, // Use lite for back-facing scenarios
    enableLandmarkRefinement: modelType !== 'lite', // Disable refinement for lite model
    enableCoordinateFiltering: true, // Always enable coordinate filtering
    inputResolution: {
      width: modelType === 'heavy' ? 256 : 192,
      height: modelType === 'heavy' ? 256 : 192
    },
    scoreThreshold: isBackFacing ? 0.3 : 0.5, // Lower threshold for back-facing
    maxPoses: 1 // Single person detection
  };

  console.log('🔧 BLAZEPOSE CUSTOM: Processor configuration:', config);
  
  return new BlazePoseCustomProcessor(config);
}