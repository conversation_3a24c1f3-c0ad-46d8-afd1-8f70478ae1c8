
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint, ImageSize } from './interfaces/common_interfaces';

/**
 * SECTION 2: Enhanced normalized keypoints to pixel coordinates conversion.
 * Converts normalized keypoints (0-1 range) to actual pixel coordinates.
 * This is essential for displaying landmarks correctly on the image.
 */
export function normalizedKeypointsToKeypoints(
    normalizedKeypoints: Keypoint[],
    imageSize: ImageSize): Keypoint[] {
  
  console.log('🔧 COORDINATE SCALING: Converting normalized to pixel coordinates');
  console.log('🔧 COORDINATE SCALING: Image size:', imageSize);
  console.log('🔧 COORDINATE SCALING: Input keypoints count:', normalizedKeypoints.length);

  const scaledKeypoints: Keypoint[] = normalizedKeypoints.map((keypoint, index) => {
    // SECTION 2: Enhanced coordinate validation and scaling

    // Validate input coordinates
    if (isNaN(keypoint.x) || isNaN(keypoint.y)) {
      console.warn(`🔧 SECTION 2: NaN coordinates in normalized keypoint ${index}, using fallback`);
      return {
        x: 0,
        y: 0,
        score: 0,
        name: keypoint.name || `keypoint_${index}`
      };
    }

    // Validate image size
    if (!imageSize || imageSize.width <= 0 || imageSize.height <= 0) {
      console.warn('🔧 SECTION 2: Invalid image size, using fallback coordinates');
      return {
        x: 0,
        y: 0,
        score: keypoint.score || 0,
        name: keypoint.name || `keypoint_${index}`
      };
    }

    // Apply image size scaling with validation
    const scaledX = keypoint.x * imageSize.width;
    const scaledY = keypoint.y * imageSize.height;

    // Validate coordinate ranges and clamp to image bounds
    const clampedX = Math.max(0, Math.min(imageSize.width, scaledX));
    const clampedY = Math.max(0, Math.min(imageSize.height, scaledY));

    const scaledKeypoint: Keypoint = {
      x: isNaN(clampedX) ? 0 : clampedX,
      y: isNaN(clampedY) ? 0 : clampedY,
      score: keypoint.score || 0,
      name: keypoint.name || `keypoint_${index}`
    };

    // Preserve and validate z-coordinate if it exists (for 3D landmarks)
    if (keypoint.z !== undefined) {
      scaledKeypoint.z = isNaN(keypoint.z) ? 0 : keypoint.z;
    }

    // Log first few keypoints for debugging
    if (index < 5) {
      console.log(`🔧 SECTION 2: Enhanced coordinate scaling ${index}:`, {
        normalized: { x: keypoint.x.toFixed(4), y: keypoint.y.toFixed(4) },
        scaled: { x: scaledKeypoint.x.toFixed(1), y: scaledKeypoint.y.toFixed(1) },
        clamped: clampedX !== scaledX || clampedY !== scaledY,
        score: keypoint.score?.toFixed(4)
      });
    }

    return scaledKeypoint;
  });

  console.log(`🔧 COORDINATE SCALING: Successfully scaled ${scaledKeypoints.length} keypoints`);
  return scaledKeypoints;
}
