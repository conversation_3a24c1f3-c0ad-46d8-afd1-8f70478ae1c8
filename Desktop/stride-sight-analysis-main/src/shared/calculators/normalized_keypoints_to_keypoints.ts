
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint, ImageSize } from './interfaces/common_interfaces';

/**
 * Converts normalized keypoints (0-1 range) to actual pixel coordinates.
 * This is essential for displaying landmarks correctly on the image.
 */
export function normalizedKeypointsToKeypoints(
    normalizedKeypoints: Keypoint[],
    imageSize: ImageSize): Keypoint[] {
  
  console.log('🔧 COORDINATE SCALING: Converting normalized to pixel coordinates');
  console.log('🔧 COORDINATE SCALING: Image size:', imageSize);
  console.log('🔧 COORDINATE SCALING: Input keypoints count:', normalizedKeypoints.length);

  const scaledKeypoints: Keypoint[] = normalizedKeypoints.map((keypoint, index) => {
    const scaledKeypoint: Keypoint = {
      x: keypoint.x * imageSize.width,
      y: keypoint.y * imageSize.height,
      score: keypoint.score,
      name: keypoint.name
    };

    // Preserve z-coordinate if it exists (for 3D landmarks)
    if (keypoint.z !== undefined) {
      scaledKeypoint.z = keypoint.z;
    }

    // Log first few keypoints for debugging
    if (index < 5) {
      console.log(`🔧 COORDINATE SCALING: Keypoint ${index}:`, {
        normalized: { x: keypoint.x.toFixed(4), y: keypoint.y.toFixed(4) },
        scaled: { x: scaledKeypoint.x.toFixed(1), y: scaledKeypoint.y.toFixed(1) },
        score: keypoint.score?.toFixed(4)
      });
    }

    return scaledKeypoint;
  });

  console.log(`🔧 COORDINATE SCALING: Successfully scaled ${scaledKeypoints.length} keypoints`);
  return scaledKeypoints;
}
