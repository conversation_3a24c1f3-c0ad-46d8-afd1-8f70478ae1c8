
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint, ImageSize } from './interfaces/common_interfaces';
import { safeTensorDispose, validateTensor, filterNaNValues } from './tensor_utils';

/**
 * Phase 3: Enhanced BlazePose tensor processing with improved coordinate handling
 */

/**
 * Validates and cleans pose coordinates with enhanced error handling.
 * This is the critical function that was causing widespread NaN issues.
 */
export function validateAndCleanPoseCoordinates(
    landmarks: Keypoint[],
    worldLandmarks: Keypoint[]): { landmarks: Keypoint[], worldLandmarks: Keypoint[] } {
  
  console.log('🔧 PHASE 3 ENHANCED: Validating pose coordinates with improved handling');
  console.log('🔧 PHASE 3 ENHANCED: Input landmarks:', landmarks.length, 'world landmarks:', worldLandmarks.length);
  
  // Enhanced validation counters
  let fixedLandmarks = 0;
  let fixedWorldLandmarks = 0;
  
  const cleanedLandmarks = landmarks.map((landmark, index) => {
    const cleanedLandmark = { ...landmark };
    let wasFixed = false;
    
    // Enhanced coordinate validation with better defaults
    if (!isValidCoordinate(cleanedLandmark.x)) {
      cleanedLandmark.x = 0.5; // Center default instead of 0
      wasFixed = true;
    }
    
    if (!isValidCoordinate(cleanedLandmark.y)) {
      cleanedLandmark.y = 0.5; // Center default instead of 0
      wasFixed = true;
    }
    
    if (cleanedLandmark.z !== undefined && !isValidCoordinate(cleanedLandmark.z)) {
      cleanedLandmark.z = 0;
      wasFixed = true;
    }
    
    // Enhanced score validation
    if (cleanedLandmark.score !== undefined && !isValidScore(cleanedLandmark.score)) {
      cleanedLandmark.score = 0.1; // Low confidence default
      wasFixed = true;
    }
    
    if (wasFixed) {
      fixedLandmarks++;
      // Only log first few fixes to avoid spam
      if (fixedLandmarks <= 5) {
        console.log(`🔧 PHASE 3 ENHANCED: Fixed landmark ${index} coordinates`);
      }
    }
    
    return cleanedLandmark;
  });
  
  const cleanedWorldLandmarks = worldLandmarks.map((landmark, index) => {
    const cleanedLandmark = { ...landmark };
    let wasFixed = false;
    
    // World coordinates use different validation (can be negative)
    if (!isValidWorldCoordinate(cleanedLandmark.x)) {
      cleanedLandmark.x = 0;
      wasFixed = true;
    }
    
    if (!isValidWorldCoordinate(cleanedLandmark.y)) {
      cleanedLandmark.y = 0;
      wasFixed = true;
    }
    
    if (cleanedLandmark.z !== undefined && !isValidWorldCoordinate(cleanedLandmark.z)) {
      cleanedLandmark.z = 0;
      wasFixed = true;
    }
    
    if (cleanedLandmark.score !== undefined && !isValidScore(cleanedLandmark.score)) {
      cleanedLandmark.score = 0.1;
      wasFixed = true;
    }
    
    if (wasFixed) {
      fixedWorldLandmarks++;
      // Only log first few fixes to avoid spam
      if (fixedWorldLandmarks <= 5) {
        console.log(`🔧 PHASE 3 ENHANCED: Fixed world landmark ${index} coordinates`);
      }
    }
    
    return cleanedLandmark;
  });
  
  // Summary logging instead of individual coordinate fixes
  if (fixedLandmarks > 0 || fixedWorldLandmarks > 0) {
    console.log('🔧 PHASE 3 ENHANCED: Coordinate validation summary:', {
      fixedLandmarks: fixedLandmarks,
      fixedWorldLandmarks: fixedWorldLandmarks,
      totalLandmarks: landmarks.length,
      totalWorldLandmarks: worldLandmarks.length,
      successRate: `${Math.round(((landmarks.length - fixedLandmarks) / landmarks.length) * 100)}%`
    });
  } else {
    console.log('✅ PHASE 3 ENHANCED: All coordinates valid, no fixes needed');
  }
  
  return {
    landmarks: cleanedLandmarks,
    worldLandmarks: cleanedWorldLandmarks
  };
}

/**
 * Enhanced coordinate validation functions
 */
function isValidCoordinate(value: number | undefined): boolean {
  return value !== undefined && 
         !isNaN(value) && 
         isFinite(value) && 
         value >= 0 && 
         value <= 1; // Normalized coordinates should be 0-1
}

function isValidWorldCoordinate(value: number | undefined): boolean {
  return value !== undefined && 
         !isNaN(value) && 
         isFinite(value) && 
         Math.abs(value) < 10; // Reasonable world coordinate bounds
}

function isValidScore(value: number | undefined): boolean {
  return value !== undefined && 
         !isNaN(value) && 
         isFinite(value) && 
         value >= 0 && 
         value <= 1;
}

/**
 * Processes raw pose detection tensors with enhanced error handling.
 */
export async function processPoseDetectionTensors(
    poseOutputs: tf.Tensor[],
    imageSize: ImageSize): Promise<{ landmarks: Keypoint[], worldLandmarks: Keypoint[] }> {
  
  console.log('🔧 PHASE 3 ENHANCED: Processing BlazePose tensors with enhanced handling');
  console.log('🔧 PHASE 3 ENHANCED: Input tensor count:', poseOutputs.length);
  
  try {
    let landmarks: Keypoint[] = [];
    let worldLandmarks: Keypoint[] = [];
    
    // Process each tensor with enhanced validation
    for (let i = 0; i < poseOutputs.length; i++) {
      const tensor = poseOutputs[i];
      
      if (!validateTensor(tensor)) {
        console.warn(`🔧 PHASE 3 ENHANCED: Skipping invalid tensor ${i}`);
        continue;
      }
      
      console.log(`🔧 PHASE 3 ENHANCED: Processing tensor ${i} shape:`, tensor.shape);
      
      // Enhanced tensor filtering
      const filteredTensor = filterNaNValues(tensor);
      
      // Determine tensor type and process accordingly
      if (isMayKnowPoseLandmarksTensor(tensor)) {
        const processedLandmarks = await processPoseLandmarksTensorEnhanced(filteredTensor, imageSize);
        if (processedLandmarks.length > 0) {
          landmarks = processedLandmarks;
          console.log('✅ PHASE 3 ENHANCED: Extracted enhanced pose landmarks');
        }
      } else if (isMayKnowWorldLandmarksTensor(tensor)) {
        const processedWorldLandmarks = await processWorldLandmarksTensorEnhanced(filteredTensor);
        if (processedWorldLandmarks.length > 0) {
          worldLandmarks = processedWorldLandmarks;
          console.log('✅ PHASE 3 ENHANCED: Extracted enhanced world landmarks');
        }
      }
      
      safeTensorDispose(filteredTensor);
    }
    
    console.log('✅ PHASE 3 ENHANCED: Tensor processing complete:', {
      landmarks: landmarks.length,
      worldLandmarks: worldLandmarks.length
    });
    
    return { landmarks, worldLandmarks };
    
  } catch (error) {
    console.error('❌ PHASE 3 ENHANCED: Tensor processing error:', error);
    return { landmarks: [], worldLandmarks: [] };
  }
}

/**
 * Enhanced tensor type detection
 */
function isMayKnowPoseLandmarksTensor(tensor: tf.Tensor): boolean {
  const shape = tensor.shape;
  return shape.length >= 2 && shape[shape.length - 1] >= 3;
}

function isMayKnowWorldLandmarksTensor(tensor: tf.Tensor): boolean {
  const shape = tensor.shape;
  return shape.length >= 2 && shape[shape.length - 1] >= 3;
}

/**
 * Enhanced pose landmarks processing
 */
async function processPoseLandmarksTensorEnhanced(
    tensor: tf.Tensor,
    imageSize: ImageSize): Promise<Keypoint[]> {
  
  console.log('🔧 PHASE 3 ENHANCED: Processing pose landmarks with enhanced validation');
  
  try {
    const data = await tensor.data();
    const shape = tensor.shape;
    
    // Handle different tensor shapes
    let numLandmarks: number;
    let coordsPerLandmark: number;
    
    if (shape.length === 3) {
      // Shape: [batch, landmarks, coords]
      numLandmarks = shape[1];
      coordsPerLandmark = shape[2];
    } else if (shape.length === 2) {
      // Shape: [landmarks, coords]
      numLandmarks = shape[0];
      coordsPerLandmark = shape[1];
    } else {
      console.warn('🔧 PHASE 3 ENHANCED: Unsupported tensor shape for landmarks');
      return [];
    }
    
    console.log('🔧 PHASE 3 ENHANCED: Tensor analysis:', {
      numLandmarks,
      coordsPerLandmark,
      dataLength: data.length
    });
    
    const landmarks: Keypoint[] = [];
    
    for (let i = 0; i < numLandmarks; i++) {
      const baseIndex = i * coordsPerLandmark;
      
      // Enhanced coordinate extraction with validation
      const rawX = data[baseIndex] || 0;
      const rawY = data[baseIndex + 1] || 0;
      const rawZ = coordsPerLandmark > 2 ? (data[baseIndex + 2] || 0) : 0;
      const rawScore = coordsPerLandmark > 3 ? (data[baseIndex + 3] || 0) : 0.5;
      
      // Enhanced coordinate normalization
      let x = rawX;
      let y = rawY;
      
      // Check if coordinates need normalization
      if (x > 1.0 || y > 1.0) {
        x = x / imageSize.width;
        y = y / imageSize.height;
      }
      
      landmarks.push({
        x: isValidCoordinate(x) ? x : 0.5,
        y: isValidCoordinate(y) ? y : 0.5,
        z: isValidCoordinate(rawZ) ? rawZ : 0,
        score: isValidScore(rawScore) ? rawScore : 0.5,
        name: `landmark_${i}`
      });
    }
    
    console.log('✅ PHASE 3 ENHANCED: Processed enhanced landmarks:', landmarks.length);
    return landmarks;
    
  } catch (error) {
    console.error('❌ PHASE 3 ENHANCED: Error processing landmarks tensor:', error);
    return [];
  }
}

/**
 * Enhanced world landmarks processing
 */
async function processWorldLandmarksTensorEnhanced(tensor: tf.Tensor): Promise<Keypoint[]> {
  console.log('🔧 PHASE 3 ENHANCED: Processing world landmarks with enhanced validation');
  
  try {
    const data = await tensor.data();
    const shape = tensor.shape;
    
    // Handle different tensor shapes
    let numLandmarks: number;
    let coordsPerLandmark: number;
    
    if (shape.length === 3) {
      numLandmarks = shape[1];
      coordsPerLandmark = shape[2];
    } else if (shape.length === 2) {
      numLandmarks = shape[0];
      coordsPerLandmark = shape[1];
    } else {
      console.warn('🔧 PHASE 3 ENHANCED: Unsupported tensor shape for world landmarks');
      return [];
    }
    
    const worldLandmarks: Keypoint[] = [];
    
    for (let i = 0; i < numLandmarks; i++) {
      const baseIndex = i * coordsPerLandmark;
      
      const rawX = data[baseIndex] || 0;
      const rawY = data[baseIndex + 1] || 0;
      const rawZ = coordsPerLandmark > 2 ? (data[baseIndex + 2] || 0) : 0;
      const rawScore = coordsPerLandmark > 3 ? (data[baseIndex + 3] || 0) : 0.5;
      
      worldLandmarks.push({
        x: isValidWorldCoordinate(rawX) ? rawX : 0,
        y: isValidWorldCoordinate(rawY) ? rawY : 0,
        z: isValidWorldCoordinate(rawZ) ? rawZ : 0,
        score: isValidScore(rawScore) ? rawScore : 0.5,
        name: `world_landmark_${i}`
      });
    }
    
    console.log('✅ PHASE 3 ENHANCED: Processed enhanced world landmarks:', worldLandmarks.length);
    return worldLandmarks;
    
  } catch (error) {
    console.error('❌ PHASE 3 ENHANCED: Error processing world landmarks tensor:', error);
    return [];
  }
}
