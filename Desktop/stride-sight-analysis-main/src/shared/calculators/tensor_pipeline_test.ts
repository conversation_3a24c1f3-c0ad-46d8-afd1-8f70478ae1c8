/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint, ImageSize } from './interfaces/common_interfaces';
import { tensorsToLandmarks, TensorsToLandmarksConfig } from './tensors_to_landmarks';
import { refineLandmarksFromHeatmap, RefineLandmarksFromHeatmapConfig } from './refine_landmarks_from_heatmap';
import { validateAndCleanPoseCoordinates } from './blazepose_tensor_processor';
import { filterNaNValues, validateTensor, safeTensorDispose, ensureTensor2D } from './tensor_utils';

/**
 * Comprehensive test suite for the enhanced tensor processing pipeline
 */
export class TensorPipelineTest {
  
  /**
   * Test the complete tensor processing pipeline
   */
  static async testCompletelyPipeline(): Promise<boolean> {
    console.log('🧪 TENSOR PIPELINE TEST: Starting comprehensive test...');
    
    try {
      // Test 1: Basic tensor creation and validation
      const testTensor1 = tf.randomNormal([1, 33, 4]); // 33 landmarks, 4 coordinates each
      const isValid1 = validateTensor(testTensor1);
      console.log('✅ Test 1 - Tensor validation:', isValid1 ? 'PASSED' : 'FAILED');
      
      // Test 2: NaN filtering
      const nanTensor = tf.tensor2d([
        [0.5, 0.6, 0.1, 0.8],
        [NaN, 0.3, 0.2, 0.9],
        [0.7, NaN, 0.15, 0.7]
      ]);
      const filteredTensor = filterNaNValues(nanTensor);
      const filteredData = await filteredTensor.data();
      const hasNaN = Array.from(filteredData).some(val => isNaN(val));
      console.log('✅ Test 2 - NaN filtering:', !hasNaN ? 'PASSED' : 'FAILED');
      
      // Test 3: Tensors to landmarks conversion
      const landmarkTensorRaw = tf.randomUniform([33, 4], 0, 1);
      const landmarkTensor = ensureTensor2D(landmarkTensorRaw, [33, 4]);
      const tensorConfig: TensorsToLandmarksConfig = {
        numLandmarks: 33,
        inputImageWidth: 640,
        inputImageHeight: 480,
        normalizeZ: 1.0,
        visibilityActivation: 'sigmoid',
        flipHorizontally: false,
        flipVertically: false
      };
      
      const landmarks = await tensorsToLandmarks(landmarkTensor, tensorConfig);
      const validLandmarks = landmarks.length === 33 && landmarks.every(lm => 
        !isNaN(lm.x) && !isNaN(lm.y) && lm.x >= 0 && lm.x <= 1 && lm.y >= 0 && lm.y <= 1
      );
      console.log('✅ Test 3 - Tensors to landmarks:', validLandmarks ? 'PASSED' : 'FAILED');
      
      // Test 4: Coordinate validation and cleaning
      const testKeypoints: Keypoint[] = [
        { x: 0.5, y: 0.6, z: 0.1, score: 0.8, name: 'nose' },
        { x: NaN, y: 0.3, z: 0.2, score: 0.9, name: 'left_eye' },
        { x: 0.7, y: NaN, z: 0.15, score: NaN, name: 'right_eye' }
      ];
      
      const testWorldKeypoints: Keypoint[] = [
        { x: 0.0, y: 0.1, z: -0.5, score: 0.8, name: 'nose' },
        { x: NaN, y: 0.2, z: 0.3, score: 0.9, name: 'left_eye' },
        { x: -0.1, y: NaN, z: NaN, score: 0.7, name: 'right_eye' }
      ];
      
      const cleaned = validateAndCleanPoseCoordinates(testKeypoints, testWorldKeypoints);
      const allValidLandmarks = cleaned.landmarks.every(lm => 
        !isNaN(lm.x) && !isNaN(lm.y) && !isNaN(lm.score || 0)
      );
      const allValidWorldLandmarks = cleaned.worldLandmarks.every(lm => 
        !isNaN(lm.x) && !isNaN(lm.y) && !isNaN(lm.z || 0)
      );
      console.log('✅ Test 4 - Coordinate validation:', 
        (allValidLandmarks && allValidWorldLandmarks) ? 'PASSED' : 'FAILED');
      
      // Test 5: Heatmap refinement (if heatmap available)
      try {
        const heatmapTensor = tf.randomUniform([1, 64, 64, 33], 0, 1) as tf.Tensor4D;
        const refinementConfig: RefineLandmarksFromHeatmapConfig = {
          kernelSize: 7,
          minConfidenceToRefine: 0.5
        };
        
        const refinedLandmarks = await refineLandmarksFromHeatmap(
          cleaned.landmarks,
          heatmapTensor,
          refinementConfig
        );
        
        const refinementWorked = refinedLandmarks.length === cleaned.landmarks.length;
        console.log('✅ Test 5 - Heatmap refinement:', refinementWorked ? 'PASSED' : 'FAILED');
        
        safeTensorDispose(heatmapTensor);
      } catch (refinementError) {
        console.log('⚠️ Test 5 - Heatmap refinement: SKIPPED (optional feature)');
      }
      
      // Cleanup test tensors
      safeTensorDispose([testTensor1, nanTensor, filteredTensor, landmarkTensorRaw, landmarkTensor]);
      
      console.log('✅ TENSOR PIPELINE TEST: All tests completed successfully!');
      return true;
      
    } catch (error) {
      console.error('❌ TENSOR PIPELINE TEST: Test failed:', error);
      return false;
    }
  }
  
  /**
   * Test tensor memory management
   */
  static testMemoryManagement(): boolean {
    console.log('🧪 MEMORY TEST: Testing tensor memory management...');
    
    const initialMemory = tf.memory();
    console.log('📊 Initial memory:', initialMemory);
    
    // Create multiple tensors
    const tensors: tf.Tensor[] = [];
    for (let i = 0; i < 10; i++) {
      tensors.push(tf.randomNormal([100, 100]));
    }
    
    const peakMemory = tf.memory();
    console.log('📊 Peak memory:', peakMemory);
    
    // Dispose all tensors
    safeTensorDispose(tensors);
    
    const finalMemory = tf.memory();
    console.log('📊 Final memory:', finalMemory);
    
    const memoryLeaked = finalMemory.numTensors > initialMemory.numTensors + 2; // Allow small variance
    console.log('✅ Memory management test:', !memoryLeaked ? 'PASSED' : 'FAILED');
    
    return !memoryLeaked;
  }
  
  /**
   * Test pipeline performance
   */
  static async testPipelinePerformance(): Promise<{ averageTime: number; passed: boolean }> {
    console.log('🧪 PERFORMANCE TEST: Testing pipeline performance...');
    
    const testRuns = 5;
    const times: number[] = [];
    
    for (let i = 0; i < testRuns; i++) {
      const startTime = performance.now();
      
      // Simulate a complete pipeline run
      const landmarkTensorRaw = tf.randomUniform([33, 4], 0, 1);
      const landmarkTensor = ensureTensor2D(landmarkTensorRaw, [33, 4]);
      const config: TensorsToLandmarksConfig = {
        numLandmarks: 33,
        inputImageWidth: 640,
        inputImageHeight: 480,
        normalizeZ: 1.0,
        visibilityActivation: 'sigmoid',
        flipHorizontally: false,
        flipVertically: false
      };
      
      const landmarks = await tensorsToLandmarks(landmarkTensor, config);
      const cleaned = validateAndCleanPoseCoordinates(landmarks, []);
      
      safeTensorDispose([landmarkTensorRaw, landmarkTensor]);
      
      const endTime = performance.now();
      times.push(endTime - startTime);
    }
    
    const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length;
    const passed = averageTime < 50; // Should complete in under 50ms
    
    console.log('✅ Performance test:', {
      averageTime: averageTime.toFixed(2) + 'ms',
      passed: passed ? 'PASSED' : 'FAILED'
    });
    
    return { averageTime, passed };
  }
  
  /**
   * Run all tests
   */
  static async runAllTests(): Promise<boolean> {
    console.log('🧪 ENHANCED TENSOR PIPELINE: Starting comprehensive test suite...');
    
    try {
      const test1 = await this.testCompletelyPipeline();
      const test2 = this.testMemoryManagement();
      const test3 = await this.testPipelinePerformance();
      
      const allPassed = test1 && test2 && test3.passed;
      
      console.log('🎯 TEST SUMMARY:', {
        pipelineTest: test1 ? 'PASSED' : 'FAILED',
        memoryTest: test2 ? 'PASSED' : 'FAILED',
        performanceTest: test3.passed ? 'PASSED' : 'FAILED',
        averageProcessingTime: test3.averageTime.toFixed(2) + 'ms',
        overallResult: allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'
      });
      
      return allPassed;
      
    } catch (error) {
      console.error('❌ TEST SUITE ERROR:', error);
      return false;
    }
  }
}

/**
 * Helper function to run tests from console
 */
export const runTensorPipelineTests = async (): Promise<boolean> => {
  return await TensorPipelineTest.runAllTests();
};