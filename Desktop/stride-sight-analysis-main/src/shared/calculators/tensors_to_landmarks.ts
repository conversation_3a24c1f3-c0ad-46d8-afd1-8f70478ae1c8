
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';
import { ensureTensor2D } from './tensor_utils';

export interface TensorsToLandmarksConfig {
  numLandmarks: number;
  inputImageWidth: number;
  inputImageHeight: number;
  normalizeZ?: number;
  visibilityActivation?: string;
  flipHorizontally?: boolean;
  flipVertically?: boolean;
}

/**
 * Converts raw landmark tensors into normalized landmark coordinates.
 * This is the critical function that was causing NaN values.
 * Now enhanced with flexible tensor input handling.
 */
export async function tensorsToLandmarks(
    landmarkTensor: tf.Tensor2D | tf.Tensor,
    config: TensorsToLandmarksConfig): Promise<Keypoint[]> {
  
  console.log('🔧 TENSOR PROCESSING: tensorsToLandmarks starting with config:', config);
  console.log('🔧 TENSOR PROCESSING: Input tensor shape:', landmarkTensor.shape, 'rank:', landmarkTensor.rank);
  
  try {
    // Ensure we have a proper 2D tensor for processing
    let processedTensor: tf.Tensor2D;
    
    if (landmarkTensor.rank === 2) {
      processedTensor = landmarkTensor as tf.Tensor2D;
    } else {
      console.log('🔧 TENSOR PROCESSING: Converting tensor to 2D format');
      // Calculate expected shape based on landmarks
      const expectedCols = 4; // x, y, z, score
      const expectedRows = config.numLandmarks;
      processedTensor = ensureTensor2D(landmarkTensor, [expectedRows, expectedCols]);
    }
    
    console.log('🔧 TENSOR PROCESSING: Using 2D tensor with shape:', processedTensor.shape);
    
    const numLandmarks = config.numLandmarks;
    const tensorRows = processedTensor.shape[0];
    const tensorCols = processedTensor.shape[1];
    
    // Validate tensor dimensions
    if (tensorRows !== numLandmarks && tensorCols !== numLandmarks * 4 && tensorCols !== numLandmarks * 3) {
      console.warn('⚠️ TENSOR PROCESSING: Tensor dimensions may not match expected landmark count');
      console.warn('⚠️ TENSOR PROCESSING: Expected:', numLandmarks, 'landmarks, got tensor shape:', processedTensor.shape);
    }
    
    const numDimensions = Math.floor(tensorCols / Math.max(tensorRows, 1));
  
    console.log('🔧 TENSOR PROCESSING: Calculated dimensions:', {
      numLandmarks,
      numDimensions,
      tensorShape: processedTensor.shape,
      totalValues: processedTensor.shape[1]
    });

    const rawLandmarks = await processedTensor.data();
  console.log('🔧 TENSOR PROCESSING: Raw tensor data length:', rawLandmarks.length);
  console.log('🔧 TENSOR PROCESSING: First 15 raw values:', Array.from(rawLandmarks.slice(0, 15)));

  const landmarks: Keypoint[] = [];

  for (let i = 0; i < numLandmarks; ++i) {
    const offset = i * numDimensions;
    let x = rawLandmarks[offset];
    let y = rawLandmarks[offset + 1];
    
    // Normalize coordinates to [0, 1] range
    if (config.inputImageWidth && config.inputImageHeight) {
      x = x / config.inputImageWidth;
      y = y / config.inputImageHeight;
    }

    // Handle coordinate flipping
    if (config.flipHorizontally) {
      x = 1.0 - x;
    }
    if (config.flipVertically) {
      y = 1.0 - y;
    }

    const landmark: Keypoint = { x, y };

    // Add z-coordinate if available
    if (numDimensions >= 3) {
      let z = rawLandmarks[offset + 2];
      if (config.normalizeZ) {
        z = z / config.normalizeZ;
      }
      landmark.z = z;
    }

    // Add visibility/score if available
    if (numDimensions >= 4) {
      let score = rawLandmarks[offset + 3];
      
      // Apply visibility activation function
      if (config.visibilityActivation === 'sigmoid') {
        score = 1.0 / (1.0 + Math.exp(-score));
      }
      
      landmark.score = score;
    }

    landmarks.push(landmark);
    
    // Log first few landmarks for debugging
    if (i < 5) {
      console.log(`🔧 TENSOR PROCESSING: Landmark ${i}:`, {
        x: landmark.x?.toFixed(4),
        y: landmark.y?.toFixed(4),
        z: landmark.z?.toFixed(4),
        score: landmark.score?.toFixed(4),
        rawValues: [rawLandmarks[offset], rawLandmarks[offset + 1], rawLandmarks[offset + 2], rawLandmarks[offset + 3]]
      });
    }
  }

    console.log(`🔧 TENSOR PROCESSING: Successfully converted ${landmarks.length} landmarks`);
    
    // Clean up processed tensor if it's different from input tensor
    if (processedTensor !== landmarkTensor) {
      processedTensor.dispose();
      console.log('🔧 TENSOR PROCESSING: Cleaned up processed tensor');
    }
    
    return landmarks;
    
  } catch (error) {
    console.error('❌ TENSOR PROCESSING: Error in tensorsToLandmarks:', error);
    
    // Fallback: return empty landmarks array
    const fallbackLandmarks: Keypoint[] = [];
    for (let i = 0; i < config.numLandmarks; i++) {
      fallbackLandmarks.push({
        x: 0.5, // Center of image
        y: 0.5,
        z: 0,
        score: 0.1, // Low confidence
        name: `landmark_${i}`
      });
    }
    
    console.log(`🔧 TENSOR PROCESSING: Returning ${fallbackLandmarks.length} fallback landmarks`);
    return fallbackLandmarks;
  }
}
