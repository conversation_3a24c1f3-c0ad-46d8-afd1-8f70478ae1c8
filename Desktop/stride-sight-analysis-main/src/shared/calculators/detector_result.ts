
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';

/**
 * PHASE 1: Detector result processing for BlazePose detection phase.
 */

export interface DetectorResult {
  boxes: tf.Tensor2D;
  logits: tf.Tensor2D;
}

/**
 * Extracts detector results from raw model output tensors.
 */
export function detectorResult(
    detectionTensors: tf.Tensor[]): DetectorResult {
  
  console.log('🔧 DETECTOR RESULT: Processing detector output tensors');
  console.log('🔧 DETECTOR RESULT: Input tensors count:', detectionTensors.length);

  if (detectionTensors.length < 2) {
    console.warn('🔧 DETECTOR RESULT: Insufficient tensors for detector result');
    // Return empty tensors as fallback
    return {
      boxes: tf.zeros([1, 4]) as tf.Tensor2D,
      logits: tf.zeros([1, 1]) as tf.Tensor2D
    };
  }

  try {
    // First tensor typically contains bounding box coordinates
    const boxes = detectionTensors[0] as tf.Tensor2D;
    
    // Second tensor typically contains detection scores/logits
    const logits = detectionTensors[1] as tf.Tensor2D;

    console.log('🔧 DETECTOR RESULT: Boxes tensor shape:', boxes.shape);
    console.log('🔧 DETECTOR RESULT: Logits tensor shape:', logits.shape);

    return {
      boxes,
      logits
    };

  } catch (error) {
    console.error('🔧 DETECTOR RESULT: Error processing detector result:', error);
    
    // Return safe fallback tensors
    return {
      boxes: tf.zeros([1, 4]) as tf.Tensor2D,
      logits: tf.zeros([1, 1]) as tf.Tensor2D
    };
  }
}

/**
 * Validates detector result tensors.
 */
export function validateDetectorResult(result: DetectorResult): boolean {
  try {
    if (!result.boxes || !result.logits) {
      console.warn('🔧 DETECTOR RESULT: Missing boxes or logits tensor');
      return false;
    }

    if (result.boxes.shape.length !== 2 || result.logits.shape.length !== 2) {
      console.warn('🔧 DETECTOR RESULT: Invalid tensor dimensions');
      return false;
    }

    console.log('🔧 DETECTOR RESULT: Validation passed');
    return true;

  } catch (error) {
    console.error('🔧 DETECTOR RESULT: Validation error:', error);
    return false;
  }
}
