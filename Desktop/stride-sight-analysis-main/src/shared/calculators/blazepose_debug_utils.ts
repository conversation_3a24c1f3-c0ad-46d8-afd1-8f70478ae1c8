/**
 * BlazePose Debug Utilities
 * Enhanced debugging tools for BlazePose tensor processing issues
 */

import * as tf from '@tensorflow/tfjs-core';
import * as poseDetection from '@tensorflow-models/pose-detection';

// Type definitions for enhanced type safety
interface RangeInfo {
  min: number;
  max: number;
  avg: number;
  count: number;
}

interface HealthCheckResult {
  isHealthy: boolean;
  issues: string[];
  severity: 'low' | 'medium' | 'high';
}

/**
 * Debug BlazePose pose structure and identify potential issues
 */
export function debugBlazePoseStructure(pose: poseDetection.Pose, frameNumber: number = 0): void {
  console.log(`🔬 BLAZEPOSE DEBUG (Frame ${frameNumber}): Comprehensive pose structure analysis`);
  
  // Basic structure validation
  const structure = {
    hasKeypoints: !!pose.keypoints,
    hasKeypoints3D: !!pose.keypoints3D,
    hasScore: typeof pose.score === 'number',
    keypointCount: pose.keypoints?.length || 0,
    keypoint3DCount: pose.keypoints3D?.length || 0,
    overallScore: pose.score
  };
  
  console.log('📊 STRUCTURE:', structure);
  
  // Analyze 2D keypoints
  if (pose.keypoints && pose.keypoints.length > 0) {
    const keypoints2D = pose.keypoints;
    const validKeypoints = keypoints2D.filter(kp => 
      !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.score)
    );
    
    const nanKeypoints = keypoints2D.filter(kp => 
      isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score)
    );
    
    const xRange = getRange(keypoints2D.map(kp => kp.x).filter(x => !isNaN(x)));
    const yRange = getRange(keypoints2D.map(kp => kp.y).filter(y => !isNaN(y)));
    const scoreRange = getRange(keypoints2D.map(kp => kp.score).filter(s => !isNaN(s)));
    
    console.log('📐 2D KEYPOINTS ANALYSIS:', {
      total: keypoints2D.length,
      valid: validKeypoints.length,
      nanCount: nanKeypoints.length,
      nanPercentage: (nanKeypoints.length / keypoints2D.length * 100).toFixed(1) + '%',
      xRange: xRange,
      yRange: yRange,
      scoreRange: scoreRange,
      coordinateSystem: determineCoordinateSystem(xRange, yRange)
    });
    
    // Log specific NaN keypoints
    if (nanKeypoints.length > 0) {
      console.warn('⚠️ NaN KEYPOINTS:', nanKeypoints.slice(0, 5).map((kp, idx) => ({
        index: keypoints2D.indexOf(kp),
        name: kp.name || `keypoint_${idx}`,
        x: kp.x,
        y: kp.y,
        score: kp.score,
        issues: [
          isNaN(kp.x) ? 'NaN x' : null,
          isNaN(kp.y) ? 'NaN y' : null,
          isNaN(kp.score) ? 'NaN score' : null
        ].filter(Boolean)
      })));
    }
  }
  
  // Analyze 3D keypoints
  if (pose.keypoints3D && pose.keypoints3D.length > 0) {
    const keypoints3D = pose.keypoints3D;
    const validKeypoints3D = keypoints3D.filter(kp => 
      !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.z)
    );
    
    const nanKeypoints3D = keypoints3D.filter(kp => 
      isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.z)
    );
    
    const xRange3D = getRange(keypoints3D.map(kp => kp.x).filter(x => !isNaN(x)));
    const yRange3D = getRange(keypoints3D.map(kp => kp.y).filter(y => !isNaN(y)));
    const zRange3D = getRange(keypoints3D.map(kp => kp.z).filter(z => !isNaN(z)));
    
    console.log('📐 3D KEYPOINTS ANALYSIS:', {
      total: keypoints3D.length,
      valid: validKeypoints3D.length,
      nanCount: nanKeypoints3D.length,
      nanPercentage: (nanKeypoints3D.length / keypoints3D.length * 100).toFixed(1) + '%',
      xRange3D: xRange3D,
      yRange3D: yRange3D,
      zRange3D: zRange3D,
      coordinateSystem: 'world coordinates (meters)'
    });
    
    // Log specific NaN 3D keypoints
    if (nanKeypoints3D.length > 0) {
      console.warn('⚠️ NaN 3D KEYPOINTS:', nanKeypoints3D.slice(0, 5).map((kp, idx) => ({
        index: keypoints3D.indexOf(kp),
        name: kp.name || `keypoint3D_${idx}`,
        x: kp.x,
        y: kp.y,
        z: kp.z,
        issues: [
          isNaN(kp.x) ? 'NaN x' : null,
          isNaN(kp.y) ? 'NaN y' : null,
          isNaN(kp.z) ? 'NaN z' : null
        ].filter(Boolean)
      })));
    }
  }
}

/**
 * Get range (min, max, avg) of a numeric array
 */
function getRange(values: number[]): RangeInfo {
  if (values.length === 0) {
    return { min: NaN, max: NaN, avg: NaN, count: 0 };
  }
  
  const min = Math.min(...values);
  const max = Math.max(...values);
  const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
  
  return { min, max, avg, count: values.length };
}

/**
 * Determine coordinate system based on value ranges
 */
function determineCoordinateSystem(xRange: RangeInfo, yRange: RangeInfo): string {
  if (xRange.min >= 0 && xRange.max <= 1 && yRange.min >= 0 && yRange.max <= 1) {
    return 'normalized [0,1]';
  } else if (xRange.min >= -1 && xRange.max <= 1 && yRange.min >= -1 && yRange.max <= 1) {
    return 'normalized [-1,1]';
  } else if (xRange.max > 100 || yRange.max > 100) {
    return 'pixel coordinates';
  } else {
    return 'unknown/mixed';
  }
}

/**
 * Debug TensorFlow memory state with enhanced metrics
 */
export function debugTensorMemory(): void {
  const memInfo = tf.memory();
  
  // Calculate enhanced debugging metrics
  const memoryMB = (memInfo.numBytes / 1024 / 1024);
  const memoryPressure = memInfo.numBytes / (500 * 1024 * 1024); // 500MB threshold
  
  // Determine complexity level based on tensor count and memory usage
  const getComplexityLevel = (): string => {
    if (memInfo.numTensors > 100 || memoryMB > 200) return 'high';
    if (memInfo.numTensors > 50 || memoryMB > 100) return 'medium';
    if (memInfo.numTensors > 20 || memoryMB > 50) return 'low';
    return 'minimal';
  };
  
  // Get performance indicators
  const getPerformanceIndicators = () => {
    const indicators: string[] = [];
    if (memInfo.numTensors > 200) indicators.push('excessive-tensors');
    if (memoryMB > 300) indicators.push('high-memory-usage');
    if (memInfo.unreliable) indicators.push('unreliable-metrics');
    if (memoryPressure > 0.9) indicators.push('critical-memory-pressure');
    return indicators.length > 0 ? indicators : ['normal'];
  };
  
  console.log('💾 TENSOR MEMORY DEBUG:', {
    numTensors: memInfo.numTensors,
    numBytes: memoryMB.toFixed(2) + ' MB',
    unreliable: memInfo.unreliable,
    backend: tf.getBackend(),
    complexityLevel: getComplexityLevel(),
    memoryPressure: (memoryPressure * 100).toFixed(1) + '%',
    performanceIndicators: getPerformanceIndicators(),
    tensorsPerMB: memInfo.numTensors > 0 ? (memInfo.numTensors / Math.max(memoryMB, 1)).toFixed(1) : '0'
  });
  
  // Enhanced warning system
  if (memoryPressure > 0.8) {
    console.warn('⚠️ HIGH MEMORY PRESSURE:', (memoryPressure * 100).toFixed(1) + '%');
  }
  
  if (memInfo.numTensors > 150) {
    console.warn('⚠️ HIGH TENSOR COUNT:', memInfo.numTensors, 'tensors active');
  }
  
  if (memInfo.unreliable) {
    console.warn('⚠️ MEMORY METRICS UNRELIABLE: Backend may not support accurate memory reporting');
  }
}

/**
 * Debug detector configuration and state
 */
export function debugDetectorState(detector: poseDetection.PoseDetector | null): void {
  const backend = tf.getBackend();
  
  console.log('🔍 DETECTOR STATE DEBUG:', {
    exists: !!detector,
    type: typeof detector,
    hasEstimateMethod: detector ? typeof detector.estimatePoses === 'function' : false,
    hasDisposeMethod: detector ? typeof detector.dispose === 'function' : false,
    backend: backend,
    backendInitialized: backend !== null && backend !== undefined,
    backendType: backend ? (backend.includes('webgl') ? 'gpu' : 'cpu') : 'unknown'
  });
}

/**
 * Comprehensive system debug information
 */
export function debugSystemInfo(): void {
  console.log('🖥️ SYSTEM DEBUG INFO:', {
    userAgent: navigator.userAgent,
    deviceMemory: (navigator as any).deviceMemory || 'unknown',
    hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
    platform: navigator.platform,
    webgl: checkWebGLSupport(),
    tensorflow: {
      backend: tf.getBackend(),
      version: tf.version_core || 'unknown',
      memory: tf.memory()
    }
  });
}

/**
 * Check WebGL support level
 */
function checkWebGLSupport(): string {
  try {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    if (!gl) return 'none';
    
    const gl2 = canvas.getContext('webgl2');
    return gl2 ? 'webgl2' : 'webgl1';
  } catch (e) {
    return 'error';
  }
}

/**
 * Quick health check function
 */
export function blazePoseHealthCheck(pose: poseDetection.Pose): HealthCheckResult {
  const issues: string[] = [];
  
  // Check basic structure
  if (!pose.keypoints || pose.keypoints.length === 0) {
    issues.push('Missing 2D keypoints');
  }
  
  if (!pose.keypoints3D || pose.keypoints3D.length === 0) {
    issues.push('Missing 3D keypoints');
  }
  
  // Check for NaN values
  const nanCount2D = pose.keypoints?.filter(kp => 
    isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score)
  ).length || 0;
  
  const nanCount3D = pose.keypoints3D?.filter(kp => 
    isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.z)
  ).length || 0;
  
  if (nanCount2D > 0) {
    issues.push(`${nanCount2D} NaN 2D keypoints`);
  }
  
  if (nanCount3D > 0) {
    issues.push(`${nanCount3D} NaN 3D keypoints`);
  }
  
  // Determine severity
  let severity: 'low' | 'medium' | 'high' = 'low';
  
  if (nanCount2D > 10 || nanCount3D > 10) {
    severity = 'high';
  } else if (nanCount2D > 5 || nanCount3D > 5) {
    severity = 'medium';
  }
  
  const isHealthy = issues.length === 0;
  
  return { isHealthy, issues, severity };
}