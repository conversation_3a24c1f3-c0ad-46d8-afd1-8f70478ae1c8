
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * Shape and geometric interfaces for BlazePose calculations.
 */

export interface Rect {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
  rotation?: number;
}

export interface NormalizedRect {
  xCenter: number;
  yCenter: number;
  width: number;
  height: number;
  rotation?: number;
}

export interface Point {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Padding {
  top: number;
  bottom: number;
  left: number;
  right: number;
}
