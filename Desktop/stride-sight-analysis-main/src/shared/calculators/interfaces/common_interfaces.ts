
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

/**
 * Core interfaces used throughout the BlazePose implementation.
 */

export interface Keypoint {
  x: number;
  y: number;
  z?: number;
  score?: number;
  name?: string;
}

export interface ImageSize {
  width: number;
  height: number;
}

export interface Detection {
  boundingBox: BoundingBox;
  landmarks?: Keypoint[];
  score?: number;
}

export interface BoundingBox {
  xMin: number;
  yMin: number;
  width: number;
  height: number;
}

export interface Pose {
  keypoints: Keypoint[];
  keypoints3D?: Keypoint[];
  score?: number;
  segmentation?: {
    maskValueToLabel: (value: number) => string;
    mask: ImageData | HTMLCanvasElement;
  };
}

export interface PoseDetectorInput {
  image: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement | ImageData;
  timestamp?: number;
}
