
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';

/**
 * Safely disposes of tensors to prevent memory leaks.
 * Critical for maintaining performance in real-time applications.
 */
export function safeTensorDispose(tensor: tf.Tensor | tf.Tensor[] | null | undefined): void {
  if (!tensor) return;
  
  try {
    if (Array.isArray(tensor)) {
      tensor.forEach(t => {
        if (t && typeof t.dispose === 'function') {
          t.dispose();
        }
      });
    } else if (typeof tensor.dispose === 'function') {
      tensor.dispose();
    }
  } catch (error) {
    console.warn('🔧 TENSOR UTILS: Error disposing tensor:', error);
  }
}

/**
 * Validates tensor shapes and values for common issues.
 * Helps identify problems in the tensor processing pipeline.
 */
export function validateTensor(tensor: tf.Tensor, expectedShape?: number[]): boolean {
  try {
    if (!tensor || !tensor.shape) {
      console.warn('🔧 TENSOR VALIDATION: Invalid tensor - missing shape');
      return false;
    }
    
    // Check for expected shape if provided
    if (expectedShape && !arraysEqual(tensor.shape, expectedShape)) {
      console.warn('🔧 TENSOR VALIDATION: Shape mismatch:', {
        actual: tensor.shape,
        expected: expectedShape
      });
      return false;
    }
    
    // Check for NaN or infinite values
    const hasInvalidValues = tf.any(tf.logicalOr(
      tf.isNaN(tensor),
      tf.logicalNot(tf.isFinite(tensor))
    ));
    
    const hasInvalidValuesSync = hasInvalidValues.dataSync()[0];
    hasInvalidValues.dispose();
    
    if (hasInvalidValuesSync) {
      console.warn('🔧 TENSOR VALIDATION: Tensor contains NaN or infinite values');
      return false;
    }
    
    console.log('🔧 TENSOR VALIDATION: Tensor validation passed');
    return true;
    
  } catch (error) {
    console.error('🔧 TENSOR VALIDATION: Error validating tensor:', error);
    return false;
  }
}

/**
 * Safely extracts data from tensors with error handling.
 */
export async function safeTensorData(tensor: tf.Tensor): Promise<Float32Array | null> {
  try {
    if (!tensor) {
      console.warn('🔧 TENSOR UTILS: Cannot extract data from null tensor');
      return null;
    }
    
    const data = await tensor.data();
    console.log('🔧 TENSOR UTILS: Successfully extracted tensor data, length:', data.length);
    return new Float32Array(data);
    
  } catch (error) {
    console.error('🔧 TENSOR UTILS: Error extracting tensor data:', error);
    return null;
  }
}

/**
 * Converts keypoints to tensor format for processing.
 */
export function keypointsToTensor(keypoints: Keypoint[]): tf.Tensor2D {
  console.log('🔧 TENSOR UTILS: Converting keypoints to tensor');
  
  const data: number[][] = keypoints.map(kp => [
    kp.x || 0,
    kp.y || 0,
    kp.z || 0,
    kp.score || 0
  ]);
  
  const tensor = tf.tensor2d(data);
  console.log('🔧 TENSOR UTILS: Created keypoints tensor with shape:', tensor.shape);
  
  return tensor;
}

/**
 * Converts tensor back to keypoints array.
 */
export async function tensorToKeypoints(tensor: tf.Tensor2D, names?: string[]): Promise<Keypoint[]> {
  console.log('🔧 TENSOR UTILS: Converting tensor to keypoints');
  
  try {
    const data = await tensor.data();
    const [numKeypoints, dimensions] = tensor.shape;
    
    const keypoints: Keypoint[] = [];
    
    for (let i = 0; i < numKeypoints; i++) {
      const startIdx = i * dimensions;
      
      const keypoint: Keypoint = {
        x: data[startIdx] || 0,
        y: data[startIdx + 1] || 0,
        score: dimensions > 3 ? data[startIdx + 3] : undefined,
        name: names?.[i] || `point_${i}`
      };
      
      // Add z coordinate if available
      if (dimensions > 2) {
        keypoint.z = data[startIdx + 2] || 0;
      }
      
      keypoints.push(keypoint);
    }
    
    console.log('🔧 TENSOR UTILS: Converted tensor to', keypoints.length, 'keypoints');
    return keypoints;
    
  } catch (error) {
    console.error('🔧 TENSOR UTILS: Error converting tensor to keypoints:', error);
    return [];
  }
}

/**
 * Helper function to compare arrays for equality.
 */
function arraysEqual(a: number[], b: number[]): boolean {
  if (a.length !== b.length) return false;
  return a.every((val, index) => val === b[index]);
}

/**
 * Applies NaN filtering to tensor data.
 * Replaces NaN values with zeros or interpolated values.
 */
/**
 * Generic NaN filtering function
 */
export function filterNaNValues(tensor: tf.Tensor): tf.Tensor {
  console.log('🔧 TENSOR UTILS: Filtering NaN values from tensor');
  
  try {
    // Replace NaN values with zeros
    const nanMask = tf.isNaN(tensor);
    const filteredTensor = tf.where(nanMask, tf.zerosLike(tensor), tensor);
    
    nanMask.dispose();
    
    console.log('🔧 TENSOR UTILS: NaN filtering complete');
    return filteredTensor;
    
  } catch (error) {
    console.error('🔧 TENSOR UTILS: Error filtering NaN values:', error);
    return tensor;
  }
}

/**
 * Type-safe NaN filtering for Tensor2D specifically
 */
export function filterNaNValues2D(tensor: tf.Tensor): tf.Tensor2D {
  console.log('🔧 TENSOR UTILS: Filtering NaN values from tensor (ensuring 2D output)');
  
  try {
    let processedTensor = tensor;
    
    // Ensure tensor is 2D
    if (tensor.rank !== 2) {
      console.log(`🔧 TENSOR UTILS: Reshaping tensor from rank ${tensor.rank} to 2D`);
      
      if (tensor.rank === 1) {
        // If 1D, assume it needs to be reshaped based on expected landmark structure
        const totalElements = tensor.shape[0];
        if (totalElements % 4 === 0) {
          // Assume [x, y, z, score] format
          processedTensor = tensor.reshape([totalElements / 4, 4]);
        } else if (totalElements % 3 === 0) {
          // Assume [x, y, z] format
          processedTensor = tensor.reshape([totalElements / 3, 3]);
        } else {
          // Default to single row
          processedTensor = tensor.reshape([1, totalElements]);
        }
      } else if (tensor.rank > 2) {
        // Flatten to 2D
        const shape = tensor.shape;
        const rows = shape[0];
        const cols = shape.slice(1).reduce((a, b) => a * b, 1);
        processedTensor = tensor.reshape([rows, cols]);
      }
    }
    
    // Apply NaN filtering
    const nanMask = tf.isNaN(processedTensor);
    const filteredTensor = tf.where(nanMask, tf.zerosLike(processedTensor), processedTensor) as tf.Tensor2D;
    
    nanMask.dispose();
    
    // Clean up intermediate tensor if we reshaped
    if (processedTensor !== tensor) {
      processedTensor.dispose();
    }
    
    console.log('🔧 TENSOR UTILS: 2D NaN filtering complete, output shape:', filteredTensor.shape);
    return filteredTensor;
    
  } catch (error) {
    console.error('❌ TENSOR UTILS: Error filtering NaN values (2D):', error);
    
    // Fallback: try to cast to 2D
    try {
      if (tensor.rank === 2) {
        return tensor as tf.Tensor2D;
      } else {
        // Emergency reshape
        const totalElements = tensor.size;
        return tensor.reshape([1, totalElements]) as tf.Tensor2D;
      }
    } catch (fallbackError) {
      console.error('❌ TENSOR UTILS: Fallback 2D casting failed:', fallbackError);
      throw new Error(`Cannot convert tensor of rank ${tensor.rank} to Tensor2D`);
    }
  }
}

/**
 * Type-safe tensor casting with shape validation
 */
export function ensureTensor2D(tensor: tf.Tensor, expectedShape?: [number, number]): tf.Tensor2D {
  console.log('🔧 TENSOR UTILS: Ensuring tensor is 2D');
  console.log('🔧 TENSOR UTILS: Input tensor shape:', tensor.shape, 'rank:', tensor.rank);
  
  try {
    // If already 2D and matches expected shape, return as-is
    if (tensor.rank === 2) {
      if (!expectedShape || (tensor.shape[0] === expectedShape[0] && tensor.shape[1] === expectedShape[1])) {
        console.log('✅ TENSOR UTILS: Tensor is already 2D with correct shape');
        return tensor as tf.Tensor2D;
      }
    }
    
    // Handle different tensor ranks
    let reshapedTensor: tf.Tensor2D;
    
    if (tensor.rank === 1) {
      const totalElements = tensor.shape[0];
      
      if (expectedShape) {
        if (expectedShape[0] * expectedShape[1] === totalElements) {
          reshapedTensor = tensor.reshape(expectedShape) as tf.Tensor2D;
        } else {
          console.warn('⚠️ TENSOR UTILS: Expected shape does not match tensor size, using automatic reshaping');
          reshapedTensor = tensor.reshape([1, totalElements]) as tf.Tensor2D;
        }
      } else {
        // Auto-detect common landmark patterns
        if (totalElements % 4 === 0) {
          // [x, y, z, score] format
          reshapedTensor = tensor.reshape([totalElements / 4, 4]) as tf.Tensor2D;
        } else if (totalElements % 3 === 0) {
          // [x, y, z] format  
          reshapedTensor = tensor.reshape([totalElements / 3, 3]) as tf.Tensor2D;
        } else if (totalElements % 2 === 0) {
          // [x, y] format
          reshapedTensor = tensor.reshape([totalElements / 2, 2]) as tf.Tensor2D;
        } else {
          // Single row
          reshapedTensor = tensor.reshape([1, totalElements]) as tf.Tensor2D;
        }
      }
    } else if (tensor.rank > 2) {
      // Flatten higher-rank tensors to 2D
      const shape = tensor.shape;
      if (expectedShape) {
        reshapedTensor = tensor.reshape(expectedShape) as tf.Tensor2D;
      } else {
        const rows = shape[0];
        const cols = shape.slice(1).reduce((a, b) => a * b, 1);
        reshapedTensor = tensor.reshape([rows, cols]) as tf.Tensor2D;
      }
    } else {
      throw new Error(`Cannot reshape tensor of rank ${tensor.rank} to 2D`);
    }
    
    console.log('✅ TENSOR UTILS: Successfully reshaped to 2D, output shape:', reshapedTensor.shape);
    return reshapedTensor;
    
  } catch (error) {
    console.error('❌ TENSOR UTILS: Error ensuring 2D tensor:', error);
    throw new Error(`Failed to convert tensor to 2D: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Create a properly typed 2D tensor from keypoints data
 */
export function keypointsToTensor2D(keypoints: any[]): tf.Tensor2D {
  console.log('🔧 TENSOR UTILS: Converting keypoints to 2D tensor');
  
  try {
    if (!keypoints || keypoints.length === 0) {
      console.warn('⚠️ TENSOR UTILS: No keypoints provided, creating empty tensor');
      return tf.zeros([1, 4]) as tf.Tensor2D;
    }
    
    // Extract coordinates from keypoints
    const keypointData = keypoints.map(kp => [
      kp.x || 0,
      kp.y || 0, 
      kp.z || 0,
      kp.score || 0
    ]);
    
    const tensor2d = tf.tensor2d(keypointData) as tf.Tensor2D;
    
    console.log('✅ TENSOR UTILS: Keypoints converted to 2D tensor, shape:', tensor2d.shape);
    return tensor2d;
    
  } catch (error) {
    console.error('❌ TENSOR UTILS: Error converting keypoints to tensor:', error);
    
    // Fallback: create zero tensor
    return tf.zeros([keypoints.length || 1, 4]) as tf.Tensor2D;
  }
}
