
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Detection } from './interfaces/common_interfaces';

/**
 * SECTION 1: Enhanced non-maximum suppression for filtering overlapping detections.
 * Includes soft NMS support and improved validation to match TensorFlow.js reference.
 */

export interface NonMaxSuppressionConfig {
  maxDetections: number;
  iouThreshold: number;
  scoreThreshold: number;
  softNmsEnabled?: boolean;
  softNmsSigma?: number;
}

/**
 * SECTION 1: Enhanced non-maximum suppression with soft NMS support.
 * Applies non-maximum suppression to filter overlapping detections.
 */
export function nonMaxSuppression(
    detections: Detection[],
    config: NonMaxSuppressionConfig): Detection[] {

  console.log('🔧 SECTION 1: Applying enhanced non-maximum suppression');
  console.log('🔧 SECTION 1: Input detections:', detections.length);
  console.log('🔧 SECTION 1: Config:', config);

  // SECTION 1: Enhanced input validation
  if (!detections || detections.length === 0) {
    console.log('🔧 SECTION 1: No detections to process');
    return [];
  }

  // Validate configuration
  if (!config || config.maxDetections <= 0 || config.iouThreshold < 0 || config.scoreThreshold < 0) {
    console.warn('🔧 SECTION 1: Invalid NMS configuration, using defaults');
    config = {
      maxDetections: config?.maxDetections || 1,
      iouThreshold: Math.max(0, Math.min(1, config?.iouThreshold || 0.3)),
      scoreThreshold: Math.max(0, Math.min(1, config?.scoreThreshold || 0.5)),
      softNmsEnabled: config?.softNmsEnabled || false,
      softNmsSigma: config?.softNmsSigma || 0.5
    };
  }

  // Validate detections and filter invalid ones
  const validDetections = detections.filter(detection => {
    if (!detection || !detection.boundingBox) {
      console.warn('🔧 SECTION 1: Invalid detection without bounding box');
      return false;
    }

    const score = detection.score || 0;
    if (isNaN(score) || score < 0) {
      console.warn('🔧 SECTION 1: Invalid detection score:', score);
      return false;
    }

    return true;
  });

  console.log(`🔧 SECTION 1: Valid detections: ${validDetections.length}/${detections.length}`);

  // Filter by score threshold
  const filteredDetections = validDetections.filter(
    detection => (detection.score || 0) >= config.scoreThreshold
  );

  console.log(`🔧 SECTION 1: After score filtering: ${filteredDetections.length} detections`);

  if (filteredDetections.length === 0) {
    return [];
  }

  // Sort by score (descending)
  filteredDetections.sort((a, b) => (b.score || 0) - (a.score || 0));

  // SECTION 1: Enhanced NMS algorithm with soft NMS support
  if (config.softNmsEnabled) {
    console.log('🔧 SECTION 1: Using soft NMS algorithm');
    return applySoftNMS(filteredDetections, config);
  } else {
    console.log('🔧 SECTION 1: Using standard NMS algorithm');
    return applyStandardNMS(filteredDetections, config);
  }
}

/**
 * SECTION 1: Standard NMS implementation
 */
function applyStandardNMS(detections: Detection[], config: NonMaxSuppressionConfig): Detection[] {
  const selectedDetections: Detection[] = [];
  const suppressedIndices = new Set<number>();

  for (let i = 0; i < detections.length && selectedDetections.length < config.maxDetections; i++) {
    if (suppressedIndices.has(i)) {
      continue;
    }

    const currentDetection = detections[i];
    selectedDetections.push(currentDetection);

    // Suppress overlapping detections
    for (let j = i + 1; j < detections.length; j++) {
      if (suppressedIndices.has(j)) {
        continue;
      }

      const iou = calculateIoU(currentDetection, detections[j]);
      if (iou > config.iouThreshold) {
        suppressedIndices.add(j);
      }
    }
  }

  console.log(`🔧 SECTION 1: Standard NMS selected detections: ${selectedDetections.length}`);
  return selectedDetections;
}

/**
 * SECTION 1: Soft NMS implementation for improved detection quality
 */
function applySoftNMS(detections: Detection[], config: NonMaxSuppressionConfig): Detection[] {
  // Create a copy of detections to avoid modifying the original
  const workingDetections = detections.map(detection => ({ ...detection }));
  const selectedDetections: Detection[] = [];
  const sigma = config.softNmsSigma || 0.5;

  for (let i = 0; i < config.maxDetections && workingDetections.length > 0; i++) {
    // Find detection with highest score
    let maxIndex = 0;
    let maxScore = workingDetections[0].score || 0;

    for (let j = 1; j < workingDetections.length; j++) {
      const score = workingDetections[j].score || 0;
      if (score > maxScore) {
        maxScore = score;
        maxIndex = j;
      }
    }

    // Select the detection with highest score
    const selectedDetection = workingDetections[maxIndex];
    selectedDetections.push(selectedDetection);
    workingDetections.splice(maxIndex, 1);

    // Apply soft suppression to remaining detections
    for (let j = workingDetections.length - 1; j >= 0; j--) {
      const iou = calculateIoU(selectedDetection, workingDetections[j]);

      if (iou > config.iouThreshold) {
        // Apply soft suppression using Gaussian decay
        const decay = Math.exp(-(iou * iou) / sigma);
        const currentScore = workingDetections[j].score || 0;
        workingDetections[j].score = currentScore * decay;

        // Remove detection if score becomes too low
        if (workingDetections[j].score! < config.scoreThreshold) {
          workingDetections.splice(j, 1);
        }
      }
    }
  }

  console.log(`🔧 SECTION 1: Soft NMS selected detections: ${selectedDetections.length}`);
  return selectedDetections;
}

/**
 * SECTION 1: Enhanced IoU calculation with comprehensive validation.
 * Calculates Intersection over Union (IoU) between two detections.
 */
function calculateIoU(detection1: Detection, detection2: Detection): number {
  // Validate inputs
  if (!detection1?.boundingBox || !detection2?.boundingBox) {
    console.warn('🔧 SECTION 1: Invalid bounding boxes for IoU calculation');
    return 0;
  }

  const box1 = detection1.boundingBox;
  const box2 = detection2.boundingBox;

  // Validate bounding box dimensions
  if (box1.width <= 0 || box1.height <= 0 || box2.width <= 0 || box2.height <= 0) {
    console.warn('🔧 SECTION 1: Invalid bounding box dimensions');
    return 0;
  }

  // Validate coordinates
  if (isNaN(box1.xMin) || isNaN(box1.yMin) || isNaN(box2.xMin) || isNaN(box2.yMin)) {
    console.warn('🔧 SECTION 1: NaN coordinates in bounding boxes');
    return 0;
  }

  // Calculate intersection with enhanced precision
  const xLeft = Math.max(box1.xMin, box2.xMin);
  const yTop = Math.max(box1.yMin, box2.yMin);
  const xRight = Math.min(box1.xMin + box1.width, box2.xMin + box2.width);
  const yBottom = Math.min(box1.yMin + box1.height, box2.yMin + box2.height);

  if (xRight <= xLeft || yBottom <= yTop) {
    return 0; // No intersection
  }

  const intersectionArea = (xRight - xLeft) * (yBottom - yTop);
  const box1Area = box1.width * box1.height;
  const box2Area = box2.width * box2.height;
  const unionArea = box1Area + box2Area - intersectionArea;

  // Validate areas
  if (intersectionArea < 0 || box1Area <= 0 || box2Area <= 0 || unionArea <= 0) {
    console.warn('🔧 SECTION 1: Invalid area calculations in IoU');
    return 0;
  }

  const iou = intersectionArea / unionArea;

  // Validate final IoU value
  if (isNaN(iou) || iou < 0 || iou > 1) {
    console.warn('🔧 SECTION 1: Invalid IoU value:', iou);
    return 0;
  }

  return iou;
}
