
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Detection } from './interfaces/common_interfaces';

/**
 * PHASE 1: Non-maximum suppression for filtering overlapping detections.
 */

export interface NonMaxSuppressionConfig {
  maxDetections: number;
  iouThreshold: number;
  scoreThreshold: number;
}

/**
 * Applies non-maximum suppression to filter overlapping detections.
 */
export function nonMaxSuppression(
    detections: Detection[],
    config: NonMaxSuppressionConfig): Detection[] {
  
  console.log('🔧 NMS: Applying non-maximum suppression');
  console.log('🔧 NMS: Input detections:', detections.length);
  console.log('🔧 NMS: Config:', config);

  if (detections.length === 0) {
    return [];
  }

  // Filter by score threshold
  const filteredDetections = detections.filter(
    detection => (detection.score || 0) >= config.scoreThreshold
  );

  console.log(`🔧 NMS: After score filtering: ${filteredDetections.length} detections`);

  // Sort by score (descending)
  filteredDetections.sort((a, b) => (b.score || 0) - (a.score || 0));

  const selectedDetections: Detection[] = [];
  const suppressedIndices = new Set<number>();

  for (let i = 0; i < filteredDetections.length && selectedDetections.length < config.maxDetections; i++) {
    if (suppressedIndices.has(i)) {
      continue;
    }

    const currentDetection = filteredDetections[i];
    selectedDetections.push(currentDetection);

    // Suppress overlapping detections
    for (let j = i + 1; j < filteredDetections.length; j++) {
      if (suppressedIndices.has(j)) {
        continue;
      }

      const iou = calculateIoU(currentDetection, filteredDetections[j]);
      if (iou > config.iouThreshold) {
        suppressedIndices.add(j);
      }
    }
  }

  console.log(`🔧 NMS: Final selected detections: ${selectedDetections.length}`);
  return selectedDetections;
}

/**
 * Calculates Intersection over Union (IoU) between two detections.
 */
function calculateIoU(detection1: Detection, detection2: Detection): number {
  const box1 = detection1.boundingBox;
  const box2 = detection2.boundingBox;

  // Calculate intersection
  const xLeft = Math.max(box1.xMin, box2.xMin);
  const yTop = Math.max(box1.yMin, box2.yMin);
  const xRight = Math.min(box1.xMin + box1.width, box2.xMin + box2.width);
  const yBottom = Math.min(box1.yMin + box1.height, box2.yMin + box2.height);

  if (xRight <= xLeft || yBottom <= yTop) {
    return 0; // No intersection
  }

  const intersectionArea = (xRight - xLeft) * (yBottom - yTop);
  const box1Area = box1.width * box1.height;
  const box2Area = box2.width * box2.height;
  const unionArea = box1Area + box2Area - intersectionArea;

  return unionArea > 0 ? intersectionArea / unionArea : 0;
}
