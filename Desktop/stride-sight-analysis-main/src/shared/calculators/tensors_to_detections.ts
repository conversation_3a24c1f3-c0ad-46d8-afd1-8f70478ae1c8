
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Detection } from './interfaces/common_interfaces';
import { Anchor } from './create_ssd_anchors';

export interface TensorsToDetectionsConfig {
  numClasses: number;
  numBoxes: number;
  numCoords: number;
  boxCoordOffset: number;
  keypointCoordOffset: number;
  numKeypoints: number;
  numValuesPerKeypoint: number;
  sigmoidScore: boolean;
  scoreClippingThresh: number;
  reverseOutputOrder: boolean;
  xScale: number;
  yScale: number;
  wScale: number;
  hScale: number;
  minScoreThresh: number;
}

/**
 * PHASE 1: Converts raw detection tensors to detection objects.
 */
export async function tensorsToDetections(
    detectionTensors: tf.Tensor[],
    anchors: Anchor[],
    config: TensorsToDetectionsConfig): Promise<Detection[]> {
  
  console.log('🔧 TENSORS TO DETECTIONS: Converting tensors to detections');
  console.log('🔧 TENSORS TO DETECTIONS: Input tensors:', detectionTensors.length);
  console.log('🔧 TENSORS TO DETECTIONS: Anchors:', anchors.length);

  const detections: Detection[] = [];
  
  if (detectionTensors.length === 0 || anchors.length === 0) {
    console.warn('🔧 TENSORS TO DETECTIONS: No tensors or anchors provided');
    return detections;
  }

  try {
    const rawBoxes = await detectionTensors[0].data();
    const rawScores = detectionTensors.length > 1 ? await detectionTensors[1].data() : null;

    const numDetections = Math.min(config.numBoxes, anchors.length);

    for (let i = 0; i < numDetections; i++) {
      const boxOffset = i * config.numCoords;
      const anchor = anchors[i];

      // Extract box coordinates
      const yCenter = rawBoxes[boxOffset] / config.yScale * anchor.height + anchor.yCenter;
      const xCenter = rawBoxes[boxOffset + 1] / config.xScale * anchor.width + anchor.xCenter;
      const h = rawBoxes[boxOffset + 2] / config.hScale * anchor.height;
      const w = rawBoxes[boxOffset + 3] / config.wScale * anchor.width;

      // Calculate bounding box
      const xMin = xCenter - w / 2;
      const yMin = yCenter - h / 2;

      // Extract score
      let score = 1.0;
      if (rawScores) {
        score = rawScores[i];
        if (config.sigmoidScore) {
          score = 1.0 / (1.0 + Math.exp(-score));
        }
      }

      // Apply score threshold
      if (score < config.minScoreThresh) {
        continue;
      }

      const detection: Detection = {
        boundingBox: {
          xMin: Math.max(0, xMin),
          yMin: Math.max(0, yMin),
          width: Math.max(0, w),
          height: Math.max(0, h)
        },
        score
      };

      detections.push(detection);
    }

    console.log(`🔧 TENSORS TO DETECTIONS: Created ${detections.length} detections`);
    return detections;

  } catch (error) {
    console.error('🔧 TENSORS TO DETECTIONS: Error converting tensors:', error);
    return [];
  }
}
