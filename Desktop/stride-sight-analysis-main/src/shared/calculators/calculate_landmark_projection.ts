
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from './interfaces/common_interfaces';
import { Rect } from './interfaces/shape_interfaces';

/**
 * SECTION 2: Enhanced landmark projection with improved coordinate transformation.
 * Projects landmarks from ROI coordinates back to the original image coordinates.
 * This is critical for displaying landmarks in the correct position on the full image.
 */
export function calculateLandmarkProjection(
    landmarks: Keypoint[],
    rect: Rect): Keypoint[] {
  
  console.log('🔧 LANDMARK PROJECTION: Projecting landmarks from ROI to image coordinates');
  console.log('🔧 LANDMARK PROJECTION: ROI rect:', rect);
  console.log('🔧 LANDMARK PROJECTION: Input landmarks count:', landmarks.length);

  const projectedLandmarks: Keypoint[] = landmarks.map((landmark, index) => {
    // SECTION 2: Enhanced coordinate validation and transformation

    // Validate input coordinates
    if (isNaN(landmark.x) || isNaN(landmark.y)) {
      console.warn(`🔧 SECTION 2: NaN coordinates detected in landmark ${index}, using fallback`);
      return {
        x: 0,
        y: 0,
        score: 0,
        name: landmark.name || `landmark_${index}`
      };
    }

    // Handle rotation if present
    let x = landmark.x;
    let y = landmark.y;

    if (rect.rotation && rect.rotation !== 0) {
      const centerX = 0.5;
      const centerY = 0.5;
      const cos = Math.cos(rect.rotation);
      const sin = Math.sin(rect.rotation);

      const translatedX = x - centerX;
      const translatedY = y - centerY;

      x = translatedX * cos - translatedY * sin + centerX;
      y = translatedX * sin + translatedY * cos + centerY;
    }

    // SECTION 2: Enhanced ROI transformation matrix
    // Apply ROI transformation with proper coordinate scaling
    const projectedX = x * rect.width + rect.xCenter - rect.width / 2;
    const projectedY = y * rect.height + rect.yCenter - rect.height / 2;

    // Validate projected coordinates
    const validatedX = isNaN(projectedX) ? 0 : Math.max(0, Math.min(1, projectedX));
    const validatedY = isNaN(projectedY) ? 0 : Math.max(0, Math.min(1, projectedY));

    const projectedLandmark: Keypoint = {
      x: validatedX,
      y: validatedY,
      score: landmark.score || 0,
      name: landmark.name || `landmark_${index}`
    };

    // Preserve and validate z-coordinate if it exists
    if (landmark.z !== undefined) {
      projectedLandmark.z = isNaN(landmark.z) ? 0 : landmark.z;
    }

    // Log first few projections for debugging
    if (index < 5) {
      console.log(`🔧 SECTION 2: Enhanced landmark projection ${index}:`, {
        roi: { x: landmark.x.toFixed(4), y: landmark.y.toFixed(4) },
        projected: { x: validatedX.toFixed(4), y: validatedY.toFixed(4) },
        score: landmark.score?.toFixed(4),
        validated: validatedX !== projectedX || validatedY !== projectedY
      });
    }

    return projectedLandmark;
  });

  console.log(`🔧 LANDMARK PROJECTION: Successfully projected ${projectedLandmarks.length} landmarks`);
  return projectedLandmarks;
}
