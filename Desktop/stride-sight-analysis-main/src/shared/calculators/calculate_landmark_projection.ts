
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from './interfaces/common_interfaces';
import { Rect } from './interfaces/shape_interfaces';

/**
 * Projects landmarks from ROI coordinates back to the original image coordinates.
 * This is critical for displaying landmarks in the correct position on the full image.
 */
export function calculateLandmarkProjection(
    landmarks: Keypoint[],
    rect: Rect): Keypoint[] {
  
  console.log('🔧 LANDMARK PROJECTION: Projecting landmarks from ROI to image coordinates');
  console.log('🔧 LANDMARK PROJECTION: ROI rect:', rect);
  console.log('🔧 LANDMARK PROJECTION: Input landmarks count:', landmarks.length);

  const projectedLandmarks: Keypoint[] = landmarks.map((landmark, index) => {
    // Handle rotation if present
    let x = landmark.x;
    let y = landmark.y;
    
    if (rect.rotation && rect.rotation !== 0) {
      const centerX = 0.5;
      const centerY = 0.5;
      const cos = Math.cos(rect.rotation);
      const sin = Math.sin(rect.rotation);
      
      const translatedX = x - centerX;
      const translatedY = y - centerY;
      
      x = translatedX * cos - translatedY * sin + centerX;
      y = translatedX * sin + translatedY * cos + centerY;
    }

    // Scale and translate to the ROI position
    const projectedX = x * rect.width + rect.xCenter - rect.width / 2;
    const projectedY = y * rect.height + rect.yCenter - rect.height / 2;

    const projectedLandmark: Keypoint = {
      x: projectedX,
      y: projectedY,
      score: landmark.score,
      name: landmark.name
    };

    // Preserve z-coordinate if it exists
    if (landmark.z !== undefined) {
      projectedLandmark.z = landmark.z;
    }

    // Log first few projections for debugging
    if (index < 5) {
      console.log(`🔧 LANDMARK PROJECTION: Landmark ${index}:`, {
        roi: { x: landmark.x.toFixed(4), y: landmark.y.toFixed(4) },
        projected: { x: projectedX.toFixed(4), y: projectedY.toFixed(4) },
        score: landmark.score?.toFixed(4)
      });
    }

    return projectedLandmark;
  });

  console.log(`🔧 LANDMARK PROJECTION: Successfully projected ${projectedLandmarks.length} landmarks`);
  return projectedLandmarks;
}
