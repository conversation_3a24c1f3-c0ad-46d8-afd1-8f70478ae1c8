
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';
import { Rect } from './interfaces/shape_interfaces';

/**
 * SECTION 2: Enhanced world landmark projection with tensor support.
 * Projects 3D world landmarks from ROI space to world coordinate system.
 * This handles the 3D coordinate transformation for world landmarks.
 */

/**
 * Projects 3D world landmarks from keypoint array (existing interface).
 */
export function calculateWorldLandmarkProjection(
    worldLandmarks: Keypoint[],
    rect: Rect): Keypoint[] {
  
  console.log('🔧 WORLD PROJECTION: Projecting 3D world landmarks');
  console.log('🔧 WORLD PROJECTION: ROI rect:', rect);
  console.log('🔧 WORLD PROJECTION: Input world landmarks count:', worldLandmarks.length);

  const projectedWorldLandmarks: Keypoint[] = worldLandmarks.map((landmark, index) => {
    // For world landmarks, we primarily need to handle scaling
    // The world coordinate system is relative to the detected pose
    const projectedLandmark: Keypoint = {
      x: landmark.x,
      y: landmark.y,
      z: landmark.z || 0,
      score: landmark.score,
      name: landmark.name
    };

    // Scale world coordinates based on ROI size for consistency
    // World coordinates are in meters, so we apply a scale factor
    const scaleFactor = Math.sqrt(rect.width * rect.height);
    projectedLandmark.x *= scaleFactor;
    projectedLandmark.y *= scaleFactor;
    if (projectedLandmark.z) {
      projectedLandmark.z *= scaleFactor;
    }

    // Log first few projections for debugging
    if (index < 5) {
      console.log(`🔧 WORLD PROJECTION: World landmark ${index}:`, {
        original: { 
          x: landmark.x?.toFixed(4), 
          y: landmark.y?.toFixed(4), 
          z: landmark.z?.toFixed(4) 
        },
        projected: { 
          x: projectedLandmark.x?.toFixed(4), 
          y: projectedLandmark.y?.toFixed(4), 
          z: projectedLandmark.z?.toFixed(4) 
        },
        scaleFactor: scaleFactor.toFixed(4),
        score: landmark.score?.toFixed(4)
      });
    }

    return projectedLandmark;
  });

  console.log(`🔧 WORLD PROJECTION: Successfully projected ${projectedWorldLandmarks.length} world landmarks`);
  return projectedWorldLandmarks;
}

/**
 * SECTION 2: Enhanced tensor-based world landmark projection.
 * Processes raw world landmark tensor as specified in gap analysis.
 */
export async function calculateWorldLandmarkProjectionFromTensor(
    worldTensor: tf.Tensor,
    rect: Rect): Promise<Keypoint[]> {

  console.log('🔧 SECTION 2: Processing world landmarks from raw tensor');
  console.log('🔧 SECTION 2: World tensor shape:', worldTensor.shape);
  console.log('🔧 SECTION 2: ROI rect:', rect);

  try {
    // Extract 3D world coordinates from tensor [1, 117] → 39 × 3 coordinates
    const worldData = await worldTensor.data();
    const worldLandmarks: Keypoint[] = [];

    // BlazePose world landmarks: 39 landmarks × 3 coordinates (x, y, z)
    const numLandmarks = 39;
    const coordinatesPerLandmark = 3;

    if (worldData.length < numLandmarks * coordinatesPerLandmark) {
      console.warn('🔧 SECTION 2: Insufficient world tensor data:', worldData.length);
      return [];
    }

    for (let i = 0; i < numLandmarks; i++) {
      const baseIndex = i * coordinatesPerLandmark;

      const landmark: Keypoint = {
        x: worldData[baseIndex],     // X in world space
        y: worldData[baseIndex + 1], // Y in world space
        z: worldData[baseIndex + 2], // Z in world space
        score: 1.0, // World coordinates don't have visibility scores
        name: `world_landmark_${i}`
      };

      worldLandmarks.push(landmark);
    }

    console.log('🔧 SECTION 2: Extracted world landmarks:', worldLandmarks.length);

    // Apply world coordinate normalization and transformation
    const normalizedWorldLandmarks = normalizeWorldCoordinates(worldLandmarks, rect);

    console.log('🔧 SECTION 2: Successfully processed world landmarks from tensor');
    return normalizedWorldLandmarks;

  } catch (error) {
    console.error('❌ SECTION 2: Error processing world landmark tensor:', error);
    return [];
  }
}

/**
 * SECTION 2: Normalizes world coordinates according to BlazePose specifications.
 */
function normalizeWorldCoordinates(worldLandmarks: Keypoint[], rect: Rect): Keypoint[] {
  console.log('🔧 SECTION 2: Normalizing world coordinates');

  return worldLandmarks.map((landmark, index) => {
    // Apply world coordinate normalization
    // World coordinates are in meters relative to the hip center
    const normalizedLandmark: Keypoint = {
      x: landmark.x,
      y: landmark.y,
      z: landmark.z || 0,
      score: landmark.score,
      name: landmark.name
    };

    // Apply scaling based on ROI size for consistency
    const scaleFactor = Math.sqrt(rect.width * rect.height);
    normalizedLandmark.x *= scaleFactor;
    normalizedLandmark.y *= scaleFactor;
    if (normalizedLandmark.z) {
      normalizedLandmark.z *= scaleFactor;
    }

    // Validate coordinates and handle NaN values
    if (isNaN(normalizedLandmark.x) || isNaN(normalizedLandmark.y) || isNaN(normalizedLandmark.z || 0)) {
      console.warn(`🔧 SECTION 2: NaN detected in world landmark ${index}, using fallback`);
      normalizedLandmark.x = 0;
      normalizedLandmark.y = 0;
      normalizedLandmark.z = 0;
      normalizedLandmark.score = 0;
    }

    return normalizedLandmark;
  });
}
