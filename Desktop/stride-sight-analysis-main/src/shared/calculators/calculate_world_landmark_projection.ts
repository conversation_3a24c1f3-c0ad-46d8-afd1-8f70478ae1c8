
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint } from './interfaces/common_interfaces';
import { Rect } from './interfaces/shape_interfaces';

/**
 * Projects 3D world landmarks from ROI space to world coordinate system.
 * This handles the 3D coordinate transformation for world landmarks.
 */
export function calculateWorldLandmarkProjection(
    worldLandmarks: Keypoint[],
    rect: Rect): Keypoint[] {
  
  console.log('🔧 WORLD PROJECTION: Projecting 3D world landmarks');
  console.log('🔧 WORLD PROJECTION: ROI rect:', rect);
  console.log('🔧 WORLD PROJECTION: Input world landmarks count:', worldLandmarks.length);

  const projectedWorldLandmarks: Keypoint[] = worldLandmarks.map((landmark, index) => {
    // For world landmarks, we primarily need to handle scaling
    // The world coordinate system is relative to the detected pose
    const projectedLandmark: Keypoint = {
      x: landmark.x,
      y: landmark.y,
      z: landmark.z || 0,
      score: landmark.score,
      name: landmark.name
    };

    // Scale world coordinates based on ROI size for consistency
    // World coordinates are in meters, so we apply a scale factor
    const scaleFactor = Math.sqrt(rect.width * rect.height);
    projectedLandmark.x *= scaleFactor;
    projectedLandmark.y *= scaleFactor;
    if (projectedLandmark.z) {
      projectedLandmark.z *= scaleFactor;
    }

    // Log first few projections for debugging
    if (index < 5) {
      console.log(`🔧 WORLD PROJECTION: World landmark ${index}:`, {
        original: { 
          x: landmark.x?.toFixed(4), 
          y: landmark.y?.toFixed(4), 
          z: landmark.z?.toFixed(4) 
        },
        projected: { 
          x: projectedLandmark.x?.toFixed(4), 
          y: projectedLandmark.y?.toFixed(4), 
          z: projectedLandmark.z?.toFixed(4) 
        },
        scaleFactor: scaleFactor.toFixed(4),
        score: landmark.score?.toFixed(4)
      });
    }

    return projectedLandmark;
  });

  console.log(`🔧 WORLD PROJECTION: Successfully projected ${projectedWorldLandmarks.length} world landmarks`);
  return projectedWorldLandmarks;
}
