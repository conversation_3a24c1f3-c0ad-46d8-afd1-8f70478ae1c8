
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { ImageSize } from './interfaces/common_interfaces';

/**
 * Gets the size of an image or video element.
 */
export function getImageSize(image: any): ImageSize {
  if (image == null) {
    return { width: 0, height: 0 };
  }

  // Check if it's a tensor
  if (image instanceof tf.Tensor && image.shape.length === 3) {
    const [height, width] = image.shape.slice(0, 2);
    return { width, height };
  }

  if (image instanceof HTMLVideoElement) {
    return { width: image.videoWidth, height: image.videoHeight };
  }

  if (image instanceof HTMLImageElement || image instanceof HTMLCanvasElement) {
    return { width: image.width, height: image.height };
  }

  if (image instanceof ImageData) {
    return { width: image.width, height: image.height };
  }

  // Check if it's a tensor (backup check)
  if (image instanceof tf.Tensor && image.shape.length === 3) {
    return { width: image.shape[1], height: image.shape[0] };
  }

  return { width: image.width || 0, height: image.height || 0 };
}

/**
 * Converts an image to a tensor.
 */
export function toImageTensor(image: any): tf.Tensor3D {
  if (image instanceof tf.Tensor && image.shape.length === 3) {
    return image as tf.Tensor3D;
  }

  return tf.browser.fromPixels(image);
}

/**
 * Gets a projective transformation matrix.
 */
export function getProjectiveTransformMatrix(
    matrix: number[],
    inputSize: ImageSize,
    outputSize: ImageSize): number[] {
  
  // This is a simplified implementation
  // The full implementation would handle complete projective transformation
  return [
    1, 0, 0, 0,
    0, 1, 0, 0
  ];
}
