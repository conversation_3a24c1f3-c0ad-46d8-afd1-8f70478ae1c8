
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { ImageSize } from './interfaces/common_interfaces';
import { getImageSize } from './image_utils';

// SECTION 2: Enhanced image processing configuration interfaces
export interface ImageProcessingConfig {
  preserveAspectRatio: boolean;
  paddingColor: [number, number, number];
  interpolationMethod: 'bilinear' | 'nearest';
  centerCrop: boolean;
  normalizationRange: [number, number];
  qualityValidation: boolean;
}

export interface ImageQualityThresholds {
  minBrightness: number;
  maxBrightness: number;
  minContrast: number;
  maxContrast: number;
}

/**
 * SECTION 2: Enhanced image conversion with improved processing options.
 * Converts various image sources to a standardized tensor format.
 * This is critical for consistent input processing across different image types.
 */
export function convertImageToTensor(
    image: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement | ImageData,
    targetSize?: ImageSize,
    keepAspectRatio: boolean = true,
    config?: Partial<ImageProcessingConfig>): tf.Tensor3D {

  console.log('🔧 SECTION 2: Enhanced image conversion to tensor');

  // SECTION 2: Apply default configuration
  const defaultConfig: ImageProcessingConfig = {
    preserveAspectRatio: keepAspectRatio,
    paddingColor: [0, 0, 0],
    interpolationMethod: 'bilinear',
    centerCrop: false,
    normalizationRange: [0, 1],
    qualityValidation: true
  };

  const processingConfig = { ...defaultConfig, ...config };
  console.log('🔧 SECTION 2: Processing config:', processingConfig);

  // SECTION 2: Enhanced input validation
  if (!image) {
    throw new Error('Invalid image input');
  }

  const imageSize = getImageSize(image);
  console.log('🔧 SECTION 2: Original size:', imageSize);

  if (!imageSize || imageSize.width <= 0 || imageSize.height <= 0) {
    throw new Error('Invalid image dimensions');
  }

  // SECTION 2: Image quality validation
  if (processingConfig.qualityValidation) {
    const qualityCheck = validateImageQuality(image);
    if (!qualityCheck.isValid) {
      console.warn('🔧 SECTION 2: Image quality warning:', qualityCheck.issues);
    }
  }

  // Convert to tensor using TensorFlow.js
  let tensor = tf.browser.fromPixels(image);
  console.log('🔧 SECTION 2: Created tensor with shape:', tensor.shape);
  
  // SECTION 2: Enhanced target size processing
  if (targetSize) {
    console.log('🔧 SECTION 2: Enhanced resizing to target size:', targetSize);

    // Validate target size
    if (targetSize.width <= 0 || targetSize.height <= 0) {
      throw new Error('Invalid target size dimensions');
    }

    if (processingConfig.preserveAspectRatio) {
      // SECTION 2: Enhanced aspect ratio preserving resize
      const aspectRatio = imageSize.width / imageSize.height;
      const targetAspectRatio = targetSize.width / targetSize.height;

      let newWidth: number, newHeight: number;

      if (processingConfig.centerCrop) {
        // Center crop approach - crop to target aspect ratio first
        if (aspectRatio > targetAspectRatio) {
          // Image is wider - crop width
          newHeight = imageSize.height;
          newWidth = Math.round(imageSize.height * targetAspectRatio);
        } else {
          // Image is taller - crop height
          newWidth = imageSize.width;
          newHeight = Math.round(imageSize.width / targetAspectRatio);
        }

        // Apply center crop
        const cropX = Math.floor((imageSize.width - newWidth) / 2);
        const cropY = Math.floor((imageSize.height - newHeight) / 2);
        tensor = tf.slice(tensor, [cropY, cropX, 0], [newHeight, newWidth, -1]);

        console.log('🔧 SECTION 2: Applied center crop:', { cropX, cropY, newWidth, newHeight });
      } else {
        // Letterbox approach - resize and pad
        if (aspectRatio > targetAspectRatio) {
          // Image is wider than target
          newWidth = targetSize.width;
          newHeight = Math.round(targetSize.width / aspectRatio);
        } else {
          // Image is taller than target
          newHeight = targetSize.height;
          newWidth = Math.round(targetSize.height * aspectRatio);
        }

        console.log('🔧 SECTION 2: Aspect ratio preserving resize:', { newWidth, newHeight });
      }

      // Apply resize with selected interpolation method
      if (processingConfig.interpolationMethod === 'bilinear') {
        tensor = tf.image.resizeBilinear(tensor, [newHeight, newWidth]);
      } else {
        tensor = tf.image.resizeNearestNeighbor(tensor, [newHeight, newWidth]);
      }

      // Apply padding if needed (letterbox only)
      if (!processingConfig.centerCrop && (newWidth !== targetSize.width || newHeight !== targetSize.height)) {
        const padTop = Math.floor((targetSize.height - newHeight) / 2);
        const padBottom = targetSize.height - newHeight - padTop;
        const padLeft = Math.floor((targetSize.width - newWidth) / 2);
        const padRight = targetSize.width - newWidth - padLeft;

        // Apply padding with specified color
        const paddingValue = processingConfig.paddingColor[0] / 255.0; // Assuming grayscale for simplicity
        tensor = tf.pad(tensor, [[padTop, padBottom], [padLeft, padRight], [0, 0]], paddingValue);
        console.log('🔧 SECTION 2: Applied enhanced padding:', {
          padTop, padBottom, padLeft, padRight,
          paddingColor: processingConfig.paddingColor
        });
      }
    } else {
      // Direct resize without aspect ratio preservation
      if (processingConfig.interpolationMethod === 'bilinear') {
        tensor = tf.image.resizeBilinear(tensor, [targetSize.height, targetSize.width]);
      } else {
        tensor = tf.image.resizeNearestNeighbor(tensor, [targetSize.height, targetSize.width]);
      }
      console.log('🔧 SECTION 2: Direct resize without aspect ratio preservation');
    }
  }
  
  // SECTION 2: Enhanced normalization with configurable range
  const [minRange, maxRange] = processingConfig.normalizationRange;
  let normalizedTensor: tf.Tensor3D;

  if (minRange !== 0 || maxRange !== 1) {
    // Custom normalization range
    const tempTensor = tf.div(tensor, 255.0); // First normalize to [0, 1]
    const rangeScale = maxRange - minRange;
    normalizedTensor = tf.add(tf.mul(tempTensor, rangeScale), minRange) as tf.Tensor3D;
    tempTensor.dispose();
    console.log(`🔧 SECTION 2: Normalized to [${minRange}, ${maxRange}] range`);
  } else {
    // Standard [0, 1] normalization
    normalizedTensor = tf.div(tensor, 255.0) as tf.Tensor3D;
    console.log('🔧 SECTION 2: Normalized to [0, 1] range');
  }

  tensor.dispose(); // Clean up intermediate tensor

  // SECTION 2: Final validation
  const finalShape = normalizedTensor.shape;
  if (finalShape.length !== 3) {
    normalizedTensor.dispose();
    throw new Error(`Invalid final tensor shape: ${finalShape}`);
  }

  console.log('🔧 SECTION 2: Enhanced image conversion complete, final tensor shape:', finalShape);
  return normalizedTensor;
}

/**
 * Preprocesses image for BlazePose model input.
 * Handles model-specific requirements like normalization and sizing.
 */
export function preprocessImageForBlazePose(
    image: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement | ImageData): tf.Tensor4D {
  
  console.log('🔧 BLAZEPOSE PREPROCESSING: Preprocessing image for BlazePose model');
  
  // BlazePose typically expects 256x256 input
  const modelInputSize = { width: 256, height: 256 };
  
  // Convert to tensor with model input size
  const tensor3d = convertImageToTensor(image, modelInputSize, true);
  
  // Add batch dimension for model input
  const tensor4d = tf.expandDims(tensor3d, 0) as tf.Tensor4D;
  
  console.log('🔧 BLAZEPOSE PREPROCESSING: Preprocessed tensor shape:', tensor4d.shape);
  return tensor4d;
}

/**
 * Extracts ROI (Region of Interest) from image tensor.
 * This is useful for focusing processing on specific image regions.
 */
export function extractROIFromTensor(
    tensor: tf.Tensor3D,
    x: number,
    y: number,
    width: number,
    height: number): tf.Tensor3D {
  
  console.log('🔧 ROI EXTRACTION: Extracting ROI from tensor');
  console.log('🔧 ROI EXTRACTION: ROI bounds:', { x, y, width, height });
  
  // Ensure bounds are within tensor dimensions
  const [tensorHeight, tensorWidth] = tensor.shape.slice(0, 2);
  
  const clampedX = Math.max(0, Math.min(x, tensorWidth - 1));
  const clampedY = Math.max(0, Math.min(y, tensorHeight - 1));
  const clampedWidth = Math.min(width, tensorWidth - clampedX);
  const clampedHeight = Math.min(height, tensorHeight - clampedY);
  
  console.log('🔧 ROI EXTRACTION: Clamped bounds:', { 
    clampedX, clampedY, clampedWidth, clampedHeight 
  });
  
  // Extract the ROI slice
  const roiTensor = tf.slice(tensor, [clampedY, clampedX, 0], [clampedHeight, clampedWidth, -1]);
  
  console.log('🔧 ROI EXTRACTION: Extracted ROI shape:', roiTensor.shape);
  return roiTensor as tf.Tensor3D;
}

/**
 * SECTION 2: Enhanced image quality validation.
 */
export function validateImageQuality(
    image: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement): { isValid: boolean; issues: string[] } {

  console.log('🔧 SECTION 2: Validating image quality');

  const issues: string[] = [];

  try {
    // Create a small sample tensor for quality analysis
    const sampleTensor = tf.browser.fromPixels(image);
    const normalizedSample = tf.div(sampleTensor, 255.0);

    // Calculate brightness (mean pixel value)
    const brightness = tf.mean(normalizedSample).dataSync()[0];

    // Calculate contrast (standard deviation)
    const mean = tf.mean(normalizedSample);
    const variance = tf.mean(tf.square(tf.sub(normalizedSample, mean)));
    const contrast = Math.sqrt(variance.dataSync()[0]);

    // Quality thresholds
    const thresholds: ImageQualityThresholds = {
      minBrightness: 0.05,
      maxBrightness: 0.95,
      minContrast: 0.01,
      maxContrast: 0.8
    };

    // Check brightness
    if (brightness < thresholds.minBrightness) {
      issues.push(`Image too dark (brightness: ${brightness.toFixed(3)})`);
    } else if (brightness > thresholds.maxBrightness) {
      issues.push(`Image too bright (brightness: ${brightness.toFixed(3)})`);
    }

    // Check contrast
    if (contrast < thresholds.minContrast) {
      issues.push(`Image has low contrast (contrast: ${contrast.toFixed(3)})`);
    } else if (contrast > thresholds.maxContrast) {
      issues.push(`Image has excessive contrast (contrast: ${contrast.toFixed(3)})`);
    }

    // Clean up tensors
    sampleTensor.dispose();
    normalizedSample.dispose();
    mean.dispose();
    variance.dispose();

    console.log('🔧 SECTION 2: Image quality analysis:', {
      brightness: brightness.toFixed(3),
      contrast: contrast.toFixed(3),
      issues: issues.length
    });

    return {
      isValid: issues.length === 0,
      issues
    };

  } catch (error) {
    console.warn('🔧 SECTION 2: Image quality validation failed:', error);
    return {
      isValid: true, // Assume valid if validation fails
      issues: ['Quality validation failed']
    };
  }
}

/**
 * SECTION 2: Enhanced detection model preprocessing.
 */
export function preprocessImageForDetection(
    image: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement,
    config?: Partial<ImageProcessingConfig>): tf.Tensor3D {

  console.log('🔧 SECTION 2: Enhanced detection preprocessing');

  // Detection model typically expects 128x128 input
  const targetSize: ImageSize = { width: 128, height: 128 };

  // Enhanced configuration for detection
  const detectionConfig: Partial<ImageProcessingConfig> = {
    preserveAspectRatio: true,
    paddingColor: [0, 0, 0],
    interpolationMethod: 'bilinear',
    centerCrop: false,
    normalizationRange: [0, 1],
    qualityValidation: false, // Skip quality validation for detection for performance
    ...config
  };

  return convertImageToTensor(image, targetSize, true, detectionConfig);
}
