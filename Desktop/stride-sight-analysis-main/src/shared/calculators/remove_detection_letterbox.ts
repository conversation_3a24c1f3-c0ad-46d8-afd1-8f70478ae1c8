/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Detection, ImageSize } from './interfaces/common_interfaces';
import { Padding } from './interfaces/shape_interfaces';

/**
 * SECTION 1: Detection letterbox removal for accurate coordinate transformation.
 * Removes letterbox padding from detection coordinates and adjusts bounding boxes
 * back to original image space with proper scaling and validation.
 */

/**
 * SECTION 1: Removes letterbox padding from detection coordinates.
 * Adjusts bounding boxes back to original image space.
 */
export function removeDetectionLetterbox(
    detections: Detection[],
    padding: Padding,
    originalSize: ImageSize): Detection[] {
  
  console.log('🔧 SECTION 1: Removing letterbox from detection coordinates');
  console.log('🔧 SECTION 1: Input detections:', detections.length);
  console.log('🔧 SECTION 1: Padding:', padding);
  console.log('🔧 SECTION 1: Original size:', originalSize);

  // Validate inputs
  if (!detections || detections.length === 0) {
    console.log('🔧 SECTION 1: No detections to process');
    return [];
  }

  if (!padding || !originalSize || originalSize.width <= 0 || originalSize.height <= 0) {
    console.warn('🔧 SECTION 1: Invalid padding or original size, returning original detections');
    return detections;
  }

  const adjustedDetections: Detection[] = detections.map((detection, index) => {
    // Validate detection structure
    if (!detection || !detection.boundingBox) {
      console.warn(`🔧 SECTION 1: Invalid detection ${index} without bounding box`);
      return detection;
    }

    const bbox = detection.boundingBox;

    // Validate bounding box coordinates
    if (isNaN(bbox.xMin) || isNaN(bbox.yMin) || isNaN(bbox.width) || isNaN(bbox.height)) {
      console.warn(`🔧 SECTION 1: NaN coordinates in detection ${index} bounding box`);
      return detection;
    }

    // Calculate effective area after padding removal
    const effectiveWidth = originalSize.width - (padding.left || 0) - (padding.right || 0);
    const effectiveHeight = originalSize.height - (padding.top || 0) - (padding.bottom || 0);

    if (effectiveWidth <= 0 || effectiveHeight <= 0) {
      console.warn('🔧 SECTION 1: Invalid effective dimensions after padding removal');
      return detection;
    }

    // Remove padding offset from bounding box coordinates
    const adjustedXMin = bbox.xMin - (padding.left || 0);
    const adjustedYMin = bbox.yMin - (padding.top || 0);

    // Scale back to original proportions
    const scaledXMin = (adjustedXMin / effectiveWidth) * originalSize.width;
    const scaledYMin = (adjustedYMin / effectiveHeight) * originalSize.height;
    const scaledWidth = (bbox.width / effectiveWidth) * originalSize.width;
    const scaledHeight = (bbox.height / effectiveHeight) * originalSize.height;

    // Validate and clamp coordinates to image bounds
    const clampedXMin = Math.max(0, Math.min(originalSize.width - 1, scaledXMin));
    const clampedYMin = Math.max(0, Math.min(originalSize.height - 1, scaledYMin));
    const clampedWidth = Math.max(1, Math.min(originalSize.width - clampedXMin, scaledWidth));
    const clampedHeight = Math.max(1, Math.min(originalSize.height - clampedYMin, scaledHeight));

    // Create adjusted detection with validated coordinates
    const adjustedDetection: Detection = {
      ...detection,
      boundingBox: {
        xMin: isNaN(clampedXMin) ? 0 : clampedXMin,
        yMin: isNaN(clampedYMin) ? 0 : clampedYMin,
        width: isNaN(clampedWidth) ? 1 : clampedWidth,
        height: isNaN(clampedHeight) ? 1 : clampedHeight
      }
    };

    // Preserve other detection properties
    if (detection.score !== undefined) {
      adjustedDetection.score = detection.score;
    }
    if (detection.landmarks) {
      // Adjust landmark coordinates if present
      adjustedDetection.landmarks = detection.landmarks.map(landmark => ({
        ...landmark,
        x: Math.max(0, Math.min(originalSize.width, 
          ((landmark.x - (padding.left || 0)) / effectiveWidth) * originalSize.width)),
        y: Math.max(0, Math.min(originalSize.height, 
          ((landmark.y - (padding.top || 0)) / effectiveHeight) * originalSize.height))
      }));
    }

    // Log first few adjustments for debugging
    if (index < 3) {
      console.log(`🔧 SECTION 1: Detection ${index} letterbox removal:`, {
        original: {
          xMin: bbox.xMin.toFixed(2),
          yMin: bbox.yMin.toFixed(2),
          width: bbox.width.toFixed(2),
          height: bbox.height.toFixed(2)
        },
        adjusted: {
          xMin: adjustedDetection.boundingBox.xMin.toFixed(2),
          yMin: adjustedDetection.boundingBox.yMin.toFixed(2),
          width: adjustedDetection.boundingBox.width.toFixed(2),
          height: adjustedDetection.boundingBox.height.toFixed(2)
        },
        clamped: clampedXMin !== scaledXMin || clampedYMin !== scaledYMin ||
                 clampedWidth !== scaledWidth || clampedHeight !== scaledHeight
      });
    }

    return adjustedDetection;
  });

  console.log(`🔧 SECTION 1: Successfully processed ${adjustedDetections.length} detection letterbox removals`);
  return adjustedDetections;
}

/**
 * SECTION 1: Validates detection bounding box coordinates.
 */
export function validateDetectionBounds(
    detection: Detection,
    imageSize: ImageSize): boolean {
  
  if (!detection?.boundingBox || !imageSize) {
    return false;
  }

  const bbox = detection.boundingBox;
  
  // Check for NaN values
  if (isNaN(bbox.xMin) || isNaN(bbox.yMin) || isNaN(bbox.width) || isNaN(bbox.height)) {
    return false;
  }

  // Check for valid dimensions
  if (bbox.width <= 0 || bbox.height <= 0) {
    return false;
  }

  // Check bounds within image
  if (bbox.xMin < 0 || bbox.yMin < 0 || 
      bbox.xMin + bbox.width > imageSize.width || 
      bbox.yMin + bbox.height > imageSize.height) {
    return false;
  }

  return true;
}

/**
 * SECTION 1: Calculates letterbox padding for detection processing.
 */
export function calculateDetectionLetterboxPadding(
    originalSize: ImageSize,
    targetSize: ImageSize): Padding {
  
  console.log('🔧 SECTION 1: Calculating detection letterbox padding');
  console.log('🔧 SECTION 1: Original size:', originalSize);
  console.log('🔧 SECTION 1: Target size:', targetSize);
  
  if (!originalSize || !targetSize || 
      originalSize.width <= 0 || originalSize.height <= 0 ||
      targetSize.width <= 0 || targetSize.height <= 0) {
    console.warn('🔧 SECTION 1: Invalid sizes for padding calculation');
    return { top: 0, bottom: 0, left: 0, right: 0 };
  }

  const originalAspect = originalSize.width / originalSize.height;
  const targetAspect = targetSize.width / targetSize.height;

  let padding: Padding;

  if (originalAspect > targetAspect) {
    // Original is wider - add vertical padding
    const scaledHeight = targetSize.width / originalAspect;
    const verticalPadding = (targetSize.height - scaledHeight) / 2;
    
    padding = {
      top: Math.max(0, verticalPadding),
      bottom: Math.max(0, verticalPadding),
      left: 0,
      right: 0
    };
  } else {
    // Original is taller - add horizontal padding
    const scaledWidth = targetSize.height * originalAspect;
    const horizontalPadding = (targetSize.width - scaledWidth) / 2;
    
    padding = {
      top: 0,
      bottom: 0,
      left: Math.max(0, horizontalPadding),
      right: Math.max(0, horizontalPadding)
    };
  }

  console.log('🔧 SECTION 1: Calculated detection padding:', padding);
  return padding;
}
