
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint, ImageSize } from './interfaces/common_interfaces';
import { Padding } from './interfaces/shape_interfaces';

/**
 * PHASE 1: Removes letterbox padding from landmark coordinates.
 * This is critical for accurate coordinate transformation.
 */

/**
 * Removes letterbox padding from landmarks, adjusting coordinates back to original image space.
 */
export function removeLandmarkLetterbox(
    landmarks: Keypoint[],
    padding: Padding,
    originalSize: ImageSize): Keypoint[] {
  
  console.log('🔧 LETTERBOX REMOVAL: Removing letterbox from landmarks');
  console.log('🔧 LETTERBOX REMOVAL: Padding:', padding);
  console.log('🔧 LETTERBOX REMOVAL: Original size:', originalSize);

  const adjustedLandmarks: Keypoint[] = landmarks.map((landmark, index) => {
    // Remove padding offset
    const adjustedX = landmark.x - padding.left;
    const adjustedY = landmark.y - padding.top;

    // Calculate effective area after padding removal
    const effectiveWidth = originalSize.width - padding.left - padding.right;
    const effectiveHeight = originalSize.height - padding.top - padding.bottom;

    // Scale back to original proportions
    const scaledX = effectiveWidth > 0 ? (adjustedX / effectiveWidth) * originalSize.width : landmark.x;
    const scaledY = effectiveHeight > 0 ? (adjustedY / effectiveHeight) * originalSize.height : landmark.y;

    const adjustedLandmark: Keypoint = {
      x: scaledX,
      y: scaledY,
      score: landmark.score,
      name: landmark.name
    };

    // Preserve z-coordinate if it exists
    if (landmark.z !== undefined) {
      adjustedLandmark.z = landmark.z;
    }

    // Log first few adjustments for debugging
    if (index < 3) {
      console.log(`🔧 LETTERBOX REMOVAL: Landmark ${index}:`, {
        original: { x: landmark.x.toFixed(2), y: landmark.y.toFixed(2) },
        adjusted: { x: scaledX.toFixed(2), y: scaledY.toFixed(2) },
        padding: { left: padding.left, top: padding.top }
      });
    }

    return adjustedLandmark;
  });

  console.log(`🔧 LETTERBOX REMOVAL: Successfully processed ${adjustedLandmarks.length} landmarks`);
  return adjustedLandmarks;
}

/**
 * Calculates letterbox padding for aspect ratio preservation.
 */
export function calculateLetterboxPadding(
    originalSize: ImageSize,
    targetSize: ImageSize): Padding {
  
  console.log('🔧 LETTERBOX CALCULATION: Calculating padding');
  
  const originalAspect = originalSize.width / originalSize.height;
  const targetAspect = targetSize.width / targetSize.height;

  let padding: Padding;

  if (originalAspect > targetAspect) {
    // Original is wider - add vertical padding
    const scaledHeight = targetSize.width / originalAspect;
    const verticalPadding = (targetSize.height - scaledHeight) / 2;
    
    padding = {
      top: verticalPadding,
      bottom: verticalPadding,
      left: 0,
      right: 0
    };
  } else {
    // Original is taller - add horizontal padding
    const scaledWidth = targetSize.height * originalAspect;
    const horizontalPadding = (targetSize.width - scaledWidth) / 2;
    
    padding = {
      top: 0,
      bottom: 0,
      left: horizontalPadding,
      right: horizontalPadding
    };
  }

  console.log('🔧 LETTERBOX CALCULATION: Calculated padding:', padding);
  return padding;
}
