
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import { Keypoint, ImageSize } from './interfaces/common_interfaces';
import { Padding } from './interfaces/shape_interfaces';

/**
 * SECTION 2: Enhanced letterbox removal with improved coordinate validation.
 * Removes letterbox padding from landmark coordinates.
 * This is critical for accurate coordinate transformation.
 */

/**
 * Removes letterbox padding from landmarks, adjusting coordinates back to original image space.
 */
export function removeLandmarkLetterbox(
    landmarks: Keypoint[],
    padding: Padding,
    originalSize: ImageSize): Keypoint[] {
  
  console.log('🔧 LETTERBOX REMOVAL: Removing letterbox from landmarks');
  console.log('🔧 LETTERBOX REMOVAL: Padding:', padding);
  console.log('🔧 LETTERBOX REMOVAL: Original size:', originalSize);

  const adjustedLandmarks: Keypoint[] = landmarks.map((landmark, index) => {
    // SECTION 2: Enhanced coordinate validation and letterbox removal

    // Validate input coordinates
    if (isNaN(landmark.x) || isNaN(landmark.y)) {
      console.warn(`🔧 SECTION 2: NaN coordinates in landmark ${index}, using fallback`);
      return {
        x: 0,
        y: 0,
        score: 0,
        name: landmark.name || `landmark_${index}`
      };
    }

    // Validate padding and original size
    if (!padding || !originalSize || originalSize.width <= 0 || originalSize.height <= 0) {
      console.warn('🔧 SECTION 2: Invalid padding or original size, returning original coordinates');
      return { ...landmark };
    }

    // Remove padding offset with validation
    const adjustedX = landmark.x - (padding.left || 0);
    const adjustedY = landmark.y - (padding.top || 0);

    // Calculate effective area after padding removal
    const effectiveWidth = originalSize.width - (padding.left || 0) - (padding.right || 0);
    const effectiveHeight = originalSize.height - (padding.top || 0) - (padding.bottom || 0);

    // Scale back to original proportions with bounds checking
    let scaledX = landmark.x;
    let scaledY = landmark.y;

    if (effectiveWidth > 0) {
      scaledX = (adjustedX / effectiveWidth) * originalSize.width;
    }
    if (effectiveHeight > 0) {
      scaledY = (adjustedY / effectiveHeight) * originalSize.height;
    }

    // Validate and clamp coordinates to image bounds
    const clampedX = Math.max(0, Math.min(originalSize.width, scaledX));
    const clampedY = Math.max(0, Math.min(originalSize.height, scaledY));

    const adjustedLandmark: Keypoint = {
      x: isNaN(clampedX) ? 0 : clampedX,
      y: isNaN(clampedY) ? 0 : clampedY,
      score: landmark.score || 0,
      name: landmark.name || `landmark_${index}`
    };

    // Preserve and validate z-coordinate if it exists
    if (landmark.z !== undefined) {
      adjustedLandmark.z = isNaN(landmark.z) ? 0 : landmark.z;
    }

    // Log first few adjustments for debugging
    if (index < 3) {
      console.log(`🔧 SECTION 2: Enhanced letterbox removal ${index}:`, {
        original: { x: landmark.x.toFixed(2), y: landmark.y.toFixed(2) },
        adjusted: { x: clampedX.toFixed(2), y: clampedY.toFixed(2) },
        padding: { left: padding.left || 0, top: padding.top || 0 },
        clamped: clampedX !== scaledX || clampedY !== scaledY
      });
    }

    return adjustedLandmark;
  });

  console.log(`🔧 LETTERBOX REMOVAL: Successfully processed ${adjustedLandmarks.length} landmarks`);
  return adjustedLandmarks;
}

/**
 * Calculates letterbox padding for aspect ratio preservation.
 */
export function calculateLetterboxPadding(
    originalSize: ImageSize,
    targetSize: ImageSize): Padding {
  
  console.log('🔧 LETTERBOX CALCULATION: Calculating padding');
  
  const originalAspect = originalSize.width / originalSize.height;
  const targetAspect = targetSize.width / targetSize.height;

  let padding: Padding;

  if (originalAspect > targetAspect) {
    // Original is wider - add vertical padding
    const scaledHeight = targetSize.width / originalAspect;
    const verticalPadding = (targetSize.height - scaledHeight) / 2;
    
    padding = {
      top: verticalPadding,
      bottom: verticalPadding,
      left: 0,
      right: 0
    };
  } else {
    // Original is taller - add horizontal padding
    const scaledWidth = targetSize.height * originalAspect;
    const horizontalPadding = (targetSize.width - scaledWidth) / 2;
    
    padding = {
      top: 0,
      bottom: 0,
      left: horizontalPadding,
      right: horizontalPadding
    };
  }

  console.log('🔧 LETTERBOX CALCULATION: Calculated padding:', padding);
  return padding;
}
