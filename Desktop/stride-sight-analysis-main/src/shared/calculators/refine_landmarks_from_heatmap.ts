
/**
 * @license
 * Copyright 2021 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * https://www.apache.org/licenses/LICENSE-2.0
 */

import * as tf from '@tensorflow/tfjs-core';
import { Keypoint } from './interfaces/common_interfaces';
import { safeTensorDispose } from './tensor_utils';

export interface RefineLandmarksFromHeatmapConfig {
  kernelSize: number;
  minConfidenceToRefine: number;
}

/**
 * Refines landmark coordinates using heatmap data for sub-pixel accuracy.
 * This function improves landmark precision by analyzing the heatmap around each point.
 */
export async function refineLandmarksFromHeatmap(
    landmarks: Keypoint[],
    heatmapTensor: tf.Tensor4D,
    config: RefineLandmarksFromHeatmapConfig): Promise<Keypoint[]> {
  
  console.log('🔧 HEATMAP REFINING: Starting refinement with config:', config);
  console.log('🔧 HEATMAP REFINING: Heatmap tensor shape:', heatmapTensor.shape);
  console.log('🔧 HEATMAP REFINING: Input landmarks count:', landmarks.length);

  const [, heatmapHeight, heatmapWidth, numLandmarks] = heatmapTensor.shape;
  
  if (landmarks.length !== numLandmarks) {
    console.warn('🔧 HEATMAP REFINING: Landmark count mismatch:', {
      landmarksLength: landmarks.length,
      heatmapChannels: numLandmarks
    });
  }

  const heatmapData = await heatmapTensor.data();
  const refinedLandmarks: Keypoint[] = [];

  for (let i = 0; i < landmarks.length && i < numLandmarks; i++) {
    const landmark = landmarks[i];
    
    // Skip refinement if confidence is too low
    if (landmark.score && landmark.score < config.minConfidenceToRefine) {
      refinedLandmarks.push({ ...landmark });
      continue;
    }

    // Convert normalized coordinates to heatmap coordinates
    const heatmapX = Math.round(landmark.x * (heatmapWidth - 1));
    const heatmapY = Math.round(landmark.y * (heatmapHeight - 1));

    // Find the maximum activation in the heatmap for this landmark
    let maxValue = -Infinity;
    let maxX = heatmapX;
    let maxY = heatmapY;

    const halfKernel = Math.floor(config.kernelSize / 2);
    
    for (let dy = -halfKernel; dy <= halfKernel; dy++) {
      for (let dx = -halfKernel; dx <= halfKernel; dx++) {
        const y = heatmapY + dy;
        const x = heatmapX + dx;
        
        if (x >= 0 && x < heatmapWidth && y >= 0 && y < heatmapHeight) {
          const index = (y * heatmapWidth + x) * numLandmarks + i;
          const value = heatmapData[index];
          
          if (value > maxValue) {
            maxValue = value;
            maxX = x;
            maxY = y;
          }
        }
      }
    }

    // Convert back to normalized coordinates
    const refinedX = maxX / (heatmapWidth - 1);
    const refinedY = maxY / (heatmapHeight - 1);

    const refinedLandmark: Keypoint = {
      ...landmark,
      x: refinedX,
      y: refinedY
    };

    refinedLandmarks.push(refinedLandmark);
    
    // Log refinement for first few landmarks
    if (i < 5) {
      console.log(`🔧 HEATMAP REFINING: Landmark ${i} refined:`, {
        original: { x: landmark.x.toFixed(4), y: landmark.y.toFixed(4) },
        refined: { x: refinedX.toFixed(4), y: refinedY.toFixed(4) },
        heatmapCoords: { x: maxX, y: maxY },
        maxValue: maxValue.toFixed(4)
      });
    }
  }

  console.log(`🔧 HEATMAP REFINING: Successfully refined ${refinedLandmarks.length} landmarks`);
  return refinedLandmarks;
}
