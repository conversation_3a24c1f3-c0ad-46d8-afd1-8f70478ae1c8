import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import Header from '@/components/Header';
import UploadPanel from '@/components/UploadPanel';
import ConfigurationPanel from '@/components/ConfigurationPanel';
import ProcessingPanel from '@/components/ProcessingPanel';
import ResultsPanel from '@/components/ResultsPanel';

type AppState = 'upload' | 'processing' | 'results';
type AnalysisMode = '2D' | '3D';
type ActivityType = 'Running' | 'Cycling';

interface VideoFile {
  file: File;
  url: string;
  name: string;
}

const Index = () => {
  const [currentState, setCurrentState] = useState<AppState>('upload');
  // SAFETY: Force 2D mode only - 3D disabled for system stability
  const [analysisMode, setAnalysisMode] = useState<AnalysisMode>('2D');
  const [activityType, setActivityType] = useState<ActivityType>('Running');
  const [sideVideo, setSideVideo] = useState<VideoFile | null>(null);
  const [rearVideo, setRearVideo] = useState<VideoFile | null>(null);
  const [height, setHeight] = useState({ feet: 5, inches: 10 });
  const [overlayStyle, setOverlayStyle] = useState('Medical');
  const [analysisQuality, setAnalysisQuality] = useState('Maximum (Every Frame)');
  const [resolution, setResolution] = useState('HD');
  const [frameRate, setFrameRate] = useState('30fps');
  const [videoSetup, setVideoSetup] = useState('Treadmill');

  // CRITICAL: Lock analysis mode during analysis to prevent switching
  const isAnalysisLocked = currentState === 'results' || currentState === 'processing';

  // Adjust analysis quality default when switching modes
  React.useEffect(() => {
    if (analysisMode === '3D' && (analysisQuality === 'Maximum (Every Frame)' || analysisQuality === 'Fast (Every 3rd Frame)' || analysisQuality === 'Balanced (Every 2nd Frame)')) {
      setAnalysisQuality('Full');
    } else if (analysisMode === '2D' && (analysisQuality === 'Full' || analysisQuality === 'Heavy')) {
      setAnalysisQuality('Maximum (Every Frame)');
    }
  }, [analysisMode, analysisQuality]);

  // Reset overlay style to Medical if it was set to removed option
  React.useEffect(() => {
    if (overlayStyle === 'Detailed') {
      setOverlayStyle('Medical');
    }
  }, [overlayStyle]);

  const canStartAnalysis = analysisMode === '3D' ? (sideVideo !== null) : (sideVideo !== null);

  const handleStartAnalysis = () => {
    if (canStartAnalysis) {
      console.log('🚀 STARTING ANALYSIS WITH SETTINGS:');
      console.log('analysisMode:', analysisMode);
      console.log('videoSetup:', videoSetup);
      console.log('analysisQuality (modelQuality):', analysisQuality);
      console.log('overlayStyle:', overlayStyle);
      console.log('activityType:', activityType);
      
      setCurrentState('processing');
      // Simulate processing time
      setTimeout(() => {
        setCurrentState('results');
      }, 3000);
    }
  };

  const handleNewAnalysis = () => {
    setCurrentState('upload');
    setSideVideo(null);
    setRearVideo(null);
  };

  // CRITICAL: Prevent mode changes during analysis
  const handleAnalysisModeChange = (mode: AnalysisMode) => {
    if (isAnalysisLocked) {
      console.warn('⚠️ Analysis mode change blocked - analysis is in progress');
      return;
    }
    // SAFETY: Block 3D mode completely for system stability
    if (mode === '3D') {
      console.warn('🚨 3D mode disabled for system safety - forcing 2D mode');
      return;
    }
    setAnalysisMode(mode);
  };

  const handleActivityTypeChange = (type: ActivityType) => {
    if (isAnalysisLocked) {
      console.warn('⚠️ Activity type change blocked - analysis is in progress');
      return;
    }
    setActivityType(type);
  };

  if (currentState === 'processing') {
    return (
      <>
        <Header 
          analysisMode={analysisMode}
          onAnalysisModeChange={handleAnalysisModeChange}
          activityType={activityType}
          onActivityTypeChange={handleActivityTypeChange}
          isLocked={isAnalysisLocked}
        />
        <ProcessingPanel analysisMode={analysisMode} />
      </>
    );
  }

  if (currentState === 'results') {
    return (
      <>
        <Header 
          analysisMode={analysisMode}
          onAnalysisModeChange={handleAnalysisModeChange}
          activityType={activityType}
          onActivityTypeChange={handleActivityTypeChange}
          isLocked={isAnalysisLocked}
        />
        <ResultsPanel 
          sideVideo={sideVideo} 
          rearVideo={rearVideo} 
          onNewAnalysis={handleNewAnalysis}
          analysisMode={analysisMode}
          videoSetup={videoSetup}
          overlayStyle={overlayStyle}
          analysisQuality={analysisQuality}
          userHeight={height}
        />
      </>
    );
  }

  return (
    <>
      <Header 
        analysisMode={analysisMode}
        onAnalysisModeChange={handleAnalysisModeChange}
        activityType={activityType}
        onActivityTypeChange={handleActivityTypeChange}
        isLocked={isAnalysisLocked}
      />
      
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold text-gray-900">
              {analysisMode} {activityType} Analysis
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Upload your videos and configure {analysisMode.toLowerCase()} pose analysis settings for professional biomechanical
              feedback with height-based scaling and real-world measurements.
            </p>
          </div>

          {/* Upload Panels */}
          {analysisMode === '2D' ? (
            <div className="grid md:grid-cols-2 gap-6">
              <UploadPanel
                title="Upload Side View Video"
                description="Record from the side to capture leg extension and body lean"
                acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB"
                video={sideVideo}
                onVideoUpload={setSideVideo}
              />
              <UploadPanel
                title="Upload Rear View Video"
                description="Record from behind to analyze stride symmetry and foot placement"
                acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB"
                video={rearVideo}
                onVideoUpload={setRearVideo}
              />
            </div>
          ) : (
            <div className="max-w-2xl mx-auto">
              <UploadPanel
                title="Upload 3D Analysis Video"
                description="Record from any angle to capture full 3D biomechanical analysis"
                acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB"
                video={sideVideo}
                onVideoUpload={setSideVideo}
              />
            </div>
          )}

          {/* Configuration Panel */}
          <ConfigurationPanel
            height={height}
            setHeight={setHeight}
            overlayStyle={overlayStyle}
            setOverlayStyle={setOverlayStyle}
            analysisQuality={analysisQuality}
            setAnalysisQuality={setAnalysisQuality}
            resolution={resolution}
            setResolution={setResolution}
            frameRate={frameRate}
            setFrameRate={setFrameRate}
            analysisMode={analysisMode}
            videoSetup={videoSetup}
            setVideoSetup={setVideoSetup}
          />

          {/* Start Analysis Button */}
          <Card>
            <CardContent className="p-6 text-center">
              <button
                onClick={handleStartAnalysis}
                disabled={!canStartAnalysis}
                className={`inline-flex items-center gap-3 px-8 py-4 rounded-lg text-lg font-semibold transition-all ${
                  canStartAnalysis
                    ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                <div className="w-6 h-6 rounded-full border-2 border-current flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-current"></div>
                </div>
                Start {analysisMode} Analysis
              </button>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default Index;
