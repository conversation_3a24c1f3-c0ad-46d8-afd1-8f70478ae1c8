
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface ConfigurationPanelProps {
  height: { feet: number; inches: number };
  setHeight: (height: { feet: number; inches: number }) => void;
  overlayStyle: string;
  setOverlayStyle: (style: string) => void;
  analysisQuality: string;
  setAnalysisQuality: (quality: string) => void;
  resolution: string;
  setResolution: (resolution: string) => void;
  frameRate: string;
  setFrameRate: (frameRate: string) => void;
  analysisMode: '2D' | '3D';
  videoSetup?: string;
  setVideoSetup?: (setup: string) => void;
}

const ConfigurationPanel: React.FC<ConfigurationPanelProps> = ({
  height,
  setHeight,
  overlayStyle,
  setOverlayStyle,
  analysisQuality,
  setAnalysisQuality,
  resolution,
  setResolution,
  frameRate,
  setFrameRate,
  analysisMode,
  videoSetup = 'Treadmill',
  setVideoSetup
}) => {
  return (
    <div className="grid md:grid-cols-2 gap-6">
      {/* User Configurations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">User Configurations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Height Input */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Height</Label>
            <div className="flex gap-2">
              <div className="flex-1">
                <Input
                  type="number"
                  value={height.feet}
                  onChange={(e) => setHeight({ ...height, feet: parseInt(e.target.value) || 0 })}
                  min="3"
                  max="8"
                  className="bg-white"
                />
                <Label className="text-xs text-gray-500 mt-1">Feet</Label>
              </div>
              <div className="flex-1">
                <Input
                  type="number"
                  value={height.inches}
                  onChange={(e) => setHeight({ ...height, inches: parseInt(e.target.value) || 0 })}
                  min="0"
                  max="11"
                  className="bg-white"
                />
                <Label className="text-xs text-gray-500 mt-1">Inches</Label>
              </div>
            </div>
          </div>

          {/* 3D Video Setup - Only show for 3D mode */}
          {analysisMode === '3D' && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">3D Video Setup</Label>
              <Select value={videoSetup} onValueChange={setVideoSetup}>
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Select video setup" />
                </SelectTrigger>
                <SelectContent className="bg-white border border-gray-200 shadow-lg">
                  <SelectItem value="Treadmill">Treadmill (5 feet stationary)</SelectItem>
                  <SelectItem value="OutAndIn">Out and In (5-40 feet movement)</SelectItem>
                  <SelectItem value="OffsetOutAndIn">Offset Out and In (5ft offset angle)</SelectItem>
                </SelectContent>
              </Select>
              <p className="text-xs text-gray-500">
                {videoSetup === 'Treadmill' && 'Film from 5 feet away while running in place'}
                {videoSetup === 'OutAndIn' && 'Start 5 feet away, run 40 feet out, turn and run back'}
                {videoSetup === 'OffsetOutAndIn' && 'Film from 5ft offset angle, runner goes out 40ft and returns - better for hip/rotation analysis'}
              </p>
            </div>
          )}

          {/* Overlay Style */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Overlay Style</Label>
            <Select value={overlayStyle} onValueChange={setOverlayStyle}>
              <SelectTrigger className="bg-white">
                <SelectValue placeholder="Select overlay style" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg">
                <SelectItem value="Medical">Medical</SelectItem>
                <SelectItem value="Minimal">Minimal</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Analysis Quality - HIDDEN FOR 3D (Intelligent Model Selection) */}
          {analysisMode !== '3D' && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Analysis Quality</Label>
              <Select value={analysisQuality} onValueChange={setAnalysisQuality}>
                <SelectTrigger className="bg-white">
                  <SelectValue placeholder="Select analysis quality" />
                </SelectTrigger>
                <SelectContent className="bg-white border border-gray-200 shadow-lg">
                  <SelectItem value="Maximum (Every Frame)">Maximum (Every Frame)</SelectItem>
                  <SelectItem value="Balanced (Every 2nd Frame)">Balanced (Every 2nd Frame)</SelectItem>
                  <SelectItem value="Fast (Every 3rd Frame)">Fast (Every 3rd Frame)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          
          {/* 3D Analysis Quality - AUTO-SELECTED (Display Only) */}
          {analysisMode === '3D' && (
            <div className="space-y-2">
              <Label className="text-sm font-medium text-gray-700">Analysis Quality</Label>
              <div className="bg-gray-50 border border-gray-200 rounded-md px-3 py-2 text-sm text-gray-600">
                🤖 Auto-Selected Based on Video Setup
                <div className="text-xs text-gray-500 mt-1">
                  • Back-facing videos → Lite Model (optimized for body detection)
                  • Front-facing videos → Full Model (comprehensive analysis)
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Technical Configurations */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">Technical Configurations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Camera Resolution */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Camera Resolution</Label>
            <Select value={resolution} onValueChange={setResolution}>
              <SelectTrigger className="bg-white">
                <SelectValue placeholder="Select resolution" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg">
                <SelectItem value="4K">4K (3840x2160)</SelectItem>
                <SelectItem value="HD">HD (1920x1080)</SelectItem>
                <SelectItem value="SD">SD (1280x720)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Frame Rate */}
          <div className="space-y-2">
            <Label className="text-sm font-medium text-gray-700">Frame Rate</Label>
            <Select value={frameRate} onValueChange={setFrameRate}>
              <SelectTrigger className="bg-white">
                <SelectValue placeholder="Select frame rate" />
              </SelectTrigger>
              <SelectContent className="bg-white border border-gray-200 shadow-lg">
                <SelectItem value="60fps">60fps</SelectItem>
                <SelectItem value="30fps">30fps</SelectItem>
                <SelectItem value="24fps">24fps</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConfigurationPanel;
