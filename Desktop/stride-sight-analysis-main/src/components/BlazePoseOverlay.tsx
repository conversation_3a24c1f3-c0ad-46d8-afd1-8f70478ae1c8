
import React, { useRef, useEffect, useState } from 'react';
import { useBlazePoseDetection } from '@/hooks/useBlazePoseDetection';
import { drawPoseConnections, drawPoseKeypoints, scaleKeypoints, drawDebugFrame } from '@/utils/poseRenderer';
import { processPoseData } from '@/utils/poseCalculations';

interface BlazePoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  modelQuality?: 'Full' | 'Heavy';
  videoSetup?: string;
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: any) => void;
  testMode?: boolean; // NEW: Enable test mode with non-clear canvas
}

const BlazePoseOverlay: React.FC<BlazePoseOverlayProps> = ({ 
  videoRef, 
  modelQuality = 'Full',
  videoSetup = 'Treadmill',
  userHeight = { feet: 5, inches: 10 },
  onPoseData,
  testMode = true // Enable test mode by default to see overlay
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const [isProcessing, setIsProcessing] = useState(false);
  const [frameCount, setFrameCount] = useState(0);
  
  // SEALED 3D PIPELINE: Use dedicated BlazePose hook with enhanced tensor processing pipeline
  const { 
    detector, 
    isInitialized, 
    debugInfo, 
    setDebugInfo, 
    detectPoses, 
    enableEnhancedTensorProcessing, 
    setEnableEnhancedTensorProcessing 
  } = useBlazePoseDetection(modelQuality, videoSetup, userHeight);

  // Convert user height to meters for precise calculations
  const userHeightMeters = (userHeight.feet * 12 + userHeight.inches) * 0.0254;

  useEffect(() => {
    console.log('🔍 BLAZEPOSE OVERLAY ENHANCED TENSOR PIPELINE AUDIT:');
    console.log('✅ SEALED 3D PIPELINE: This component uses BlazePose ONLY with ENHANCED TENSOR PROCESSING');
    console.log('❌ SEALED 3D PIPELINE: NO MoveNet allowed here');
    console.log('🎯 Model Quality:', modelQuality);
    console.log('📍 Video Setup:', videoSetup);
    console.log('👤 User Height:', `${userHeight.feet}'${userHeight.inches}"`, `(${userHeightMeters.toFixed(2)}m)`);
    console.log('🧪 TEST MODE:', testMode ? 'ENABLED (Non-Clear Canvas for Visibility)' : 'DISABLED (Normal Operation)');
    console.log('🔧 ENHANCED TENSOR PROCESSING:', enableEnhancedTensorProcessing ? 'ENABLED' : 'DISABLED');
    
    // NEW: Show intelligent model selection
    const isBackFacing = videoSetup?.toLowerCase().includes('out') || videoSetup?.toLowerCase().includes('offset');
    console.log('🤖 INTELLIGENT MODEL SELECTION:', {
      videoSetup: videoSetup,
      isBackFacing: isBackFacing,
      expectedModel: isBackFacing ? 'BlazePose Lite (face-independent)' : `BlazePose ${modelQuality} (standard)`,
      reasoning: isBackFacing ? 'Back-facing scenario detected - using Lite model for better body detection' : 'Front-facing scenario - using requested model quality'
    });
    
    // AUDIT: Check for enhanced tensor processing capabilities
    console.log('🔍 AUDIT: Checking Enhanced BlazePose Tensor Processing capabilities...');
    console.log('✅ AUDIT: BlazePose with ENHANCED TENSOR PIPELINE provides keypoints3D (world landmarks) with x,y,z coordinates');
    console.log('✅ AUDIT: Regular keypoints provide 2D x,y screen coordinates with tensor refinement');
    console.log('✅ AUDIT: World landmarks provide 3D x,y,z real-world coordinates in meters with enhanced processing');
    console.log('🛠️ AUDIT: Enhanced tensor-based coordinate repair system active');
    console.log('🔧 AUDIT: Custom tensor processing pipeline:', enableEnhancedTensorProcessing ? 'ACTIVE' : 'DISABLED');
    
    if (!isInitialized || !videoRef.current || !canvasRef.current) {
      console.log('⏸️ SEALED 3D PIPELINE: BlazePose not ready yet - waiting for initialization');
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) return;

    const processFrame = async () => {
      // CRASH FIX: Don't continue loop when video is paused/ended
      if (video.paused || video.ended) {
        console.log('⏸️ Video paused/ended - stopping frame processing');
        return;
      }

      try {
        setIsProcessing(true);
        
        // Set canvas size to match video display size
        const rect = video.getBoundingClientRect();
        if (canvas.width !== rect.width || canvas.height !== rect.height) {
          canvas.width = rect.width;
          canvas.height = rect.height;
          canvas.style.position = 'absolute';
          canvas.style.top = '0px';
          canvas.style.left = '0px';
          canvas.style.width = `${rect.width}px`;
          canvas.style.height = `${rect.height}px`;
          canvas.style.pointerEvents = 'none';
          canvas.style.zIndex = '10';
          console.log('🎨 Canvas dimensions set:', { width: canvas.width, height: canvas.height });
        }
        
        // TEST MODE: Use very light accumulation - don't cover skeleton
        if (testMode) {
          // Much lighter overlay to avoid covering skeleton
          ctx.fillStyle = 'rgba(0, 0, 0, 0.005)'; // Reduced from 0.02 to 0.005
          ctx.fillRect(0, 0, canvas.width, canvas.height);
        } else {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        // SEALED 3D PIPELINE: Use BlazePose detector with REDUCED LOGGING
        if (frameCount % 60 === 0) { // Log only every 2 seconds
          console.log('🔍 SEALED 3D PIPELINE: Calling BlazePose detectPoses...');
        }
        const poses = await detectPoses(video);
        if (frameCount % 60 === 0) {
          console.log(`🚀 SEALED 3D PIPELINE: BlazePose returned ${poses.length} poses`);
        }
        
        // AUDIT: Log 3D world landmarks if available
        if (poses.length > 0) {
          const pose = poses[0];
          console.log(`🎯 SEALED 3D PIPELINE: Drawing BlazePose with ${pose.keypoints?.length || 0} keypoints (enhanced)`);
          
          // 🔍 RAW POSE OBJECT INSPECTION - REDUCED LOGGING
          if (frameCount % 180 === 0) { // Log only every 6 seconds
            console.log('🔬 RAW BLAZEPOSE OBJECT STRUCTURE:', {
              keypointsLength: pose.keypoints?.length || 0,
              keypoints3DLength: pose.keypoints3D?.length || 0,
              score: pose.score
            });
          }
          
          // AUDIT: Check for 3D world landmarks - MINIMAL LOGGING
          if (pose.keypoints3D && pose.keypoints3D.length > 0) {
            if (frameCount % 300 === 0) { // Log only every 10 seconds
              const valid3DKeypoints = pose.keypoints3D.filter((kp: any) => !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.z));
              console.log('📈 3D KEYPOINT SUMMARY:', {
                total: pose.keypoints3D.length,
                valid: valid3DKeypoints.length,
                validationRate: `${((valid3DKeypoints.length/pose.keypoints3D.length)*100).toFixed(1)}%`
              });
            }
          }
          
          // AUDIT: Check regular 2D keypoints - MINIMAL LOGGING
          if (pose.keypoints && pose.keypoints.length > 0) {
            if (frameCount % 300 === 0) { // Log only every 10 seconds
              const validKeypoints = pose.keypoints.filter((kp: any) => !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.score));
              console.log('📈 2D KEYPOINT SUMMARY:', {
                total: pose.keypoints.length,
                valid: validKeypoints.length,
                validationRate: `${((validKeypoints.length/pose.keypoints.length)*100).toFixed(1)}%`
              });
            }
          }
          
          // PHASE 1 & 2: Use BlazePose-specific enhanced rendering with height scaling
          drawPoseWithEnhancedHeightScaling(ctx, pose, canvas.width, canvas.height, testMode);
          
          // Draw debug frame in test mode
          if (testMode) {
            drawDebugFrame(ctx, frameCount);
          }
          
          // Send pose data to parent component with enhanced 3D info
          if (onPoseData) {
            onPoseData({
              frameNumber: frameCount,
              timestamp: Date.now(),
              pose: pose,
              has3D: !!(pose.keypoints3D && pose.keypoints3D.length > 0),
              modelQuality: modelQuality,
              videoSetup: videoSetup,
              userHeight: userHeight,
              pipeline: '3D-BlazePose-ENHANCED-TENSOR-PROCESSING',
              testMode: testMode
            });
          }
        } else {
          console.log('⚠️ SEALED 3D PIPELINE: No poses detected by BlazePose (official)');
          
          // TEST MODE: Draw a debug indicator when no poses detected
          if (testMode) {
            ctx.fillStyle = 'rgba(255, 0, 0, 0.3)';
            ctx.fillRect(10, 10, 20, 20);
            ctx.fillStyle = 'white';
            ctx.font = '12px Arial';
            ctx.fillText('No Pose', 35, 25);
          }
        }
        
        setFrameCount(prev => prev + 1);
        setDebugInfo(`3D Frame ${frameCount}: BlazePose (${modelQuality}) - ${videoSetup} - ${userHeight.feet}'${userHeight.inches}" - OFFICIAL PATTERNS${testMode ? ' - TEST MODE' : ''}`);
        
      } catch (error) {
        console.error('❌ SEALED 3D PIPELINE: BlazePose processing error (official):', error);
        setDebugInfo(`BlazePose Error: ${error.message}`);
      } finally {
        setIsProcessing(false);
      }

      animationFrameRef.current = requestAnimationFrame(processFrame);
    };

    console.log('✅ SEALED 3D PIPELINE: Starting BlazePose processing loop (official patterns) - NO MoveNet');
    processFrame();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isInitialized, videoRef, modelQuality, videoSetup, frameCount, onPoseData, detectPoses, setDebugInfo, userHeightMeters, userHeight, testMode]);

  // PHASE 1 & 2: BlazePose-specific enhanced height scaling for 3D pipeline
  const calculateEnhancedHeightScale = (keypoints: any[], canvasHeight: number) => {
    // Enhanced keypoint validation with multiple confidence thresholds
    const getValidKeypoint = (index: number, minScore = 0.3) => {
      const kp = keypoints[index];
      return kp && kp.score >= minScore ? kp : null;
    };
    
    // Primary approach: Full body height (nose to ankles)
    const nose = getValidKeypoint(0, 0.2);           // Head reference (lower threshold)
    const leftAnkle = getValidKeypoint(27, 0.2);     // Left ankle  
    const rightAnkle = getValidKeypoint(28, 0.2);    // Right ankle
    
    let apparentHeightPixels = 0;
    let method = 'fallback';
    
    if (nose && (leftAnkle || rightAnkle)) {
      // Use best available ankle
      const ankle = (leftAnkle && rightAnkle) ? 
        { y: (leftAnkle.y + rightAnkle.y) / 2 } : 
        (leftAnkle || rightAnkle);
      
      apparentHeightPixels = Math.abs(ankle.y - nose.y);
      method = 'nose-to-ankle';
      console.log(`📏 SEALED 3D PIPELINE: Using ${method}: ${apparentHeightPixels.toFixed(1)}px`);
    } 
    // Secondary approach: Shoulders to hips (torso-based estimation)
    else {
      const leftShoulder = getValidKeypoint(11, 0.3);
      const rightShoulder = getValidKeypoint(12, 0.3);
      const leftHip = getValidKeypoint(23, 0.3);
      const rightHip = getValidKeypoint(24, 0.3);
      
      if ((leftShoulder || rightShoulder) && (leftHip || rightHip)) {
        const shoulderY = (leftShoulder && rightShoulder) ? 
          (leftShoulder.y + rightShoulder.y) / 2 :
          (leftShoulder || rightShoulder).y;
        
        const hipY = (leftHip && rightHip) ? 
          (leftHip.y + rightHip.y) / 2 :
          (leftHip || rightHip).y;
        
        const torsoHeight = Math.abs(hipY - shoulderY);
        
        // Torso is approximately 35-40% of total height
        apparentHeightPixels = torsoHeight / 0.37;
        method = 'torso-estimation';
      }
      // Tertiary approach: Use canvas height as reasonable estimate
      else {
        apparentHeightPixels = canvasHeight * 0.75; // Assume person takes 75% of frame
        method = 'canvas-fallback';
      }
    }
    
    // Enhanced distance calculation based on video setup
    let referenceDistance = 5; // feet - baseline for all setups
    
    if (videoSetup === 'Treadmill') {
      referenceDistance = 5; // Constant 5 feet
    } else if (videoSetup === 'OutAndIn' || videoSetup === 'OffsetOutAndIn') {
      // Dynamic distance could be implemented here based on video timeline
      referenceDistance = 5; // Using start distance for now
    }
    
    // Expected height in pixels at reference distance (enhanced FOV calculation)
    const referenceHeightPixels = canvasHeight * 0.65; // Refined estimate
    
    // Calculate actual distance and scale factor
    const actualDistance = referenceDistance * (referenceHeightPixels / apparentHeightPixels);
    const scaleFactor = referenceDistance / actualDistance;
    const clampedScale = Math.max(0.4, Math.min(1.8, scaleFactor)); // More reasonable bounds
    
    // Log height scaling only every 60 frames
    if (frameCount % 60 === 0) {
      console.log(`📏 HEIGHT SCALING: ${method} scale=${clampedScale.toFixed(2)} (${apparentHeightPixels.toFixed(0)}px apparent height)`);
    }
    
    return clampedScale;
  };

  const drawPoseWithEnhancedHeightScaling = (ctx: CanvasRenderingContext2D, pose: any, width: number, height: number, testMode: boolean = false) => {
    const keypoints = pose.keypoints;
    const scale = calculateEnhancedHeightScale(keypoints, height);
    
    // Minimal logging only every 60 frames
    if (frameCount % 60 === 0) {
      console.log(`🎨 Drawing BlazePose with scale ${scale.toFixed(2)}${testMode ? ' (TEST MODE)' : ''}`);
    }
    
    // TEST MODE: Use brighter colors for better visibility in accumulating canvas
    const colorMultiplier = testMode ? 1.5 : 1.0;
    
    // Draw keypoints with enhanced scaling and confidence-based sizing - ADAPTIVE THRESHOLD
    keypoints.forEach((keypoint: any, index: number) => {
      const adaptiveKeypointThreshold = 0.05; // Very low for NaN-repaired coordinates
      if (keypoint.score > adaptiveKeypointThreshold) {
        // Even larger, more visible keypoints for better skeleton visibility
        const baseRadius = Math.max(8, 8 * scale); // Increased minimum size to 8px
        const confidenceMultiplier = Math.max(0.9, keypoint.score); // Higher minimum confidence display
        const radius = baseRadius * confidenceMultiplier;
        
        // PHASE 2: Add shadow for better visibility
        ctx.shadowColor = 'rgba(0, 0, 0, 0.7)';
        ctx.shadowBlur = 3;
        ctx.shadowOffsetX = 1;
        ctx.shadowOffsetY = 1;
        
        ctx.beginPath();
        ctx.arc(keypoint.x, keypoint.y, radius, 0, 2 * Math.PI);
        ctx.fillStyle = getEnhancedKeypointColor(index, keypoint.score, colorMultiplier);
        ctx.fill();
        
        // Reset shadow for stroke
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;
        
        // Enhanced stroke for better visibility
        ctx.strokeStyle = testMode ? '#FFFFFF' : '#333333'; // Lighter stroke for better contrast
        ctx.lineWidth = Math.max(3, 4 * scale); // Increased minimum stroke width
        ctx.stroke();
      }
    });

    // Draw pose connections with enhanced scaling
    drawEnhancedBlazePoseConnections(ctx, keypoints, scale, testMode);
  };

  const getEnhancedKeypointColor = (index: number, score: number, multiplier: number = 1.0): string => {
    // Enhanced keypoint coloring based on body part and confidence - BlazePose specific
    const alpha = Math.max(0.6, Math.min(1.0, score * multiplier)); 
    
    // BlazePose keypoint mapping for meaningful colors
    if (index <= 10) return `rgba(255, 255, 0, ${alpha})`; // Face/head - bright yellow
    if (index <= 12) return `rgba(0, 255, 0, ${alpha})`; // Shoulders - bright green  
    if (index <= 22) return `rgba(255, 165, 0, ${alpha})`; // Arms/hands - bright orange
    if (index <= 24) return `rgba(0, 255, 255, ${alpha})`; // Torso/hips - bright cyan
    return `rgba(255, 0, 255, ${alpha})`; // Legs/feet - bright magenta
  };

  const drawEnhancedBlazePoseConnections = (ctx: CanvasRenderingContext2D, keypoints: any[], scale: number, testMode: boolean = false) => {
    // Complete BlazePose 33-point connections (all 33 keypoints)
    const connections = [
      // Face connections (more detailed for BlazePose)
      [0, 1], [1, 2], [2, 3], [3, 7], // Right side face
      [0, 4], [4, 5], [5, 6], [6, 8], // Left side face
      [9, 10], // Mouth
      
      // Main body structure
      [11, 12], // Shoulders
      [11, 13], [13, 15], // Left arm
      [12, 14], [14, 16], // Right arm
      [11, 23], [12, 24], [23, 24], // Torso
      [23, 25], [25, 27], // Left leg
      [24, 26], [26, 28], // Right leg
      
      // Hand details (BlazePose specific)
      [15, 17], [15, 19], [15, 21], // Left hand
      [16, 18], [16, 20], [16, 22], // Right hand
      
      // Foot details (BlazePose specific)
      [27, 29], [27, 31], // Left foot
      [28, 30], [28, 32], // Right foot
    ];

    // PHASE 2: Enhanced visibility with shadows and better colors
    ctx.strokeStyle = testMode ? '#00FFFF' : '#32CD32'; // Lime green for consistency
    ctx.lineWidth = Math.max(4, 6 * scale); // Even thicker lines for 3D visibility
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';
    
    // Add shadow for better visibility
    ctx.shadowColor = 'rgba(0, 0, 0, 0.5)';
    ctx.shadowBlur = 2;
    ctx.shadowOffsetX = 1;
    ctx.shadowOffsetY = 1;
    
    let connectionsDrawn = 0;
    connections.forEach(([startIdx, endIdx]) => {
      const startPoint = keypoints[startIdx];
      const endPoint = keypoints[endIdx];

      // ADAPTIVE THRESHOLD FOR BACK-FACING POSES - very low threshold for repaired NaN coordinates
      const adaptiveThreshold = 0.05; // Very low to work with NaN-repaired coordinates
      if (startPoint && endPoint && 
          startPoint.score !== undefined && endPoint.score !== undefined &&
          startPoint.score > adaptiveThreshold && endPoint.score > adaptiveThreshold) {
        // Enhanced line width for better visibility
        const avgConfidence = (startPoint.score + endPoint.score) / 2;
        ctx.lineWidth = Math.max(4, 5 * scale * avgConfidence); // Increased minimum line width
        
        ctx.beginPath();
        ctx.moveTo(startPoint.x, startPoint.y);
        ctx.lineTo(endPoint.x, endPoint.y);
        ctx.stroke();
        connectionsDrawn++;
      }
    });
    
    // Reset shadow effects
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 0;
    
    // Log only every 60 frames to reduce console spam
    if (frameCount % 60 === 0) {
      console.log(`🎨 BLAZEPOSE SKELETON: Drew ${connectionsDrawn}/${connections.length} connections (scale: ${scale.toFixed(2)})`);
    }
  };

  const getSetupDisplayName = () => {
    switch(videoSetup) {
      case 'Treadmill': return 'Treadmill';
      case 'OutAndIn': return 'Out and In';
      case 'OffsetOutAndIn': return 'Offset Out and In';
      default: return videoSetup;
    }
  };

  return (
    <>
      <canvas
        ref={canvasRef}
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          pointerEvents: 'none',
          zIndex: 10,
          border: testMode ? '2px solid cyan' : 'none', // Visual indicator in test mode
        }}
      />
      <div 
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          background: testMode ? 'rgba(0,255,255,0.7)' : 'rgba(0,0,0,0.6)',
          color: testMode ? 'black' : 'white',
          padding: '6px',
          borderRadius: '4px',
          fontSize: '11px',
          zIndex: 11,
          border: testMode ? '1px solid yellow' : 'none',
          maxWidth: '200px'
        }}
      >
        BlazePose {modelQuality}
        {testMode && <><br />🧪 TEST MODE</>}
        <br />
        {getSetupDisplayName()}
        <br />
        {userHeight.feet}'{userHeight.inches}" • {isProcessing ? 'Processing...' : 'Ready'}
        <br />
        Frame: {frameCount}
      </div>
    </>
  );
};

export default BlazePoseOverlay;
