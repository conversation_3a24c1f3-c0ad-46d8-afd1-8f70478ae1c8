
import React, { useRef, useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Play, Pause, RotateCcw } from 'lucide-react';
import PoseOverlay from './PoseOverlay';

interface VideoPlayerProps {
  videoUrl: string;
  analysisType: 'running' | 'cycling';
  viewType: 'side' | 'rear';
  overlayStyle?: string;
  analysisMode?: '2D' | '3D';
  videoSetup?: string;
  modelQuality?: 'Full' | 'Heavy';
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: any) => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ 
  videoUrl, 
  analysisType, 
  viewType,
  overlayStyle = 'Medical',
  analysisMode = '2D',
  videoSetup = 'Treadmill',
  modelQuality = 'Full',
  userHeight = { feet: 5, inches: 10 },
  onPoseData
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  // PHASE 1.1: Audio controls removed for gait analysis focus
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  // PHASE 1.1: Default to 0.5x speed for better gait analysis
  const [playbackRate, setPlaybackRate] = useState(0.5);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    // REFINED: Set default playback speed to 0.5x and disable audio
    video.playbackRate = 0.5;
    video.muted = true; // Disable audio by default for gait analysis focus
    video.volume = 0; // Ensure no audio

    const handleTimeUpdate = () => setCurrentTime(video.currentTime);
    const handleDurationChange = () => setDuration(video.duration);
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);

    video.addEventListener('timeupdate', handleTimeUpdate);
    video.addEventListener('durationchange', handleDurationChange);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('timeupdate', handleTimeUpdate);
      video.removeEventListener('durationchange', handleDurationChange);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, []);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
  };

  // PHASE 1.1: Audio controls removed - mute functionality not needed

  const restart = () => {
    const video = videoRef.current;
    if (!video) return;

    video.currentTime = 0;
    video.play();
  };

  const handleProgressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const video = videoRef.current;
    if (!video) return;

    const newTime = (parseFloat(e.target.value) / 100) * duration;
    video.currentTime = newTime;
  };

  const handleSpeedChange = (speed: number) => {
    const video = videoRef.current;
    if (!video) return;

    video.playbackRate = speed;
    setPlaybackRate(speed);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const progress = duration > 0 ? (currentTime / duration) * 100 : 0;
  const speedOptions = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 2];

  console.log('🎥 VideoPlayer render - PIPELINE VERIFICATION:');
  console.log('analysisMode:', analysisMode, 'type:', typeof analysisMode);
  console.log('analysisType:', analysisType, 'type:', typeof analysisType);
  console.log('viewType:', viewType, 'type:', typeof viewType);
  console.log('videoSetup:', videoSetup, 'type:', typeof videoSetup);
  console.log('modelQuality:', modelQuality, 'type:', typeof modelQuality);
  console.log('overlayStyle:', overlayStyle, 'type:', typeof overlayStyle);
  console.log('userHeight:', userHeight);

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        <div className="relative bg-black">
          <video
            ref={videoRef}
            src={videoUrl}
            className="w-full h-auto"
            onClick={togglePlay}
            muted
            playsInline
            preload="metadata"
            style={{
              imageRendering: 'crisp-edges',
              objectFit: 'contain'
            }}
          />
          
          <PoseOverlay 
            videoRef={videoRef} 
            analysisType={analysisType}
            analysisMode={analysisMode}
            viewType={viewType}
            overlayStyle={overlayStyle}
            modelQuality={modelQuality}
            videoSetup={videoSetup}
            userHeight={userHeight}
            onPoseData={onPoseData}
          />
          
          {/* PHASE 1.1: Enhanced Video Controls - Audio removed, better layout */}
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent p-4">
            {/* Enhanced Progress Bar - More prominent */}
            <div className="mb-3">
              <div className="relative">
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={progress}
                  onChange={handleProgressChange}
                  className="w-full h-3 bg-white/20 rounded-lg appearance-none cursor-pointer slider hover:bg-white/30 transition-colors"
                  style={{
                    background: `linear-gradient(to right, #00FF41 0%, #00FF41 ${progress}%, rgba(255,255,255,0.2) ${progress}%, rgba(255,255,255,0.2) 100%)`
                  }}
                />
                <div className="absolute -top-8 left-0 right-0 flex justify-between text-xs text-white/70">
                  <span>{formatTime(currentTime)}</span>
                  <span>{formatTime(duration)}</span>
                </div>
              </div>
            </div>

            {/* Control Buttons - Improved layout */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <button
                  onClick={togglePlay}
                  className="flex items-center justify-center w-12 h-12 bg-white/20 hover:bg-white/30 rounded-full transition-all duration-200 hover:scale-105"
                >
                  {isPlaying ? (
                    <Pause className="w-6 h-6 text-white" />
                  ) : (
                    <Play className="w-6 h-6 text-white ml-0.5" />
                  )}
                </button>

                <button
                  onClick={restart}
                  className="flex items-center justify-center w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full transition-all duration-200 hover:scale-105"
                >
                  <RotateCcw className="w-5 h-5 text-white" />
                </button>
              </div>

              {/* Speed Control - Enhanced styling */}
              <div className="flex items-center gap-3 bg-white/10 rounded-lg px-3 py-2">
                <span className="text-white text-sm font-medium">Speed:</span>
                <select
                  value={playbackRate}
                  onChange={(e) => handleSpeedChange(parseFloat(e.target.value))}
                  className="bg-white/20 text-white text-sm rounded-md px-3 py-1 border-none outline-none hover:bg-white/30 transition-colors cursor-pointer"
                >
                  {speedOptions.map(speed => (
                    <option key={speed} value={speed} className="text-black bg-white">
                      {speed}x
                    </option>
                  ))}
                </select>
              </div>

              {/* Time Display - Enhanced */}
              <div className="text-white text-sm font-mono bg-white/10 rounded-lg px-3 py-2">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VideoPlayer;
