import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import VideoPlayer from '@/components/VideoPlayer';
import PronationAnalysis from '@/components/PronationAnalysis';
import AnglePanel from '@/components/AnglePanel';

interface VideoFile {
  file: File;
  url: string;
  name: string;
}

interface ResultsPanelProps {
  sideVideo: VideoFile | null;
  rearVideo?: VideoFile | null;
  onNewAnalysis: () => void;
  analysisMode?: '2D' | '3D';
  videoSetup?: string;
  overlayStyle?: string;
  analysisQuality?: string;
  userHeight?: { feet: number; inches: number };
}

const ResultsPanel: React.FC<ResultsPanelProps> = ({ 
  sideVideo, 
  rearVideo, 
  onNewAnalysis, 
  analysisMode = '2D',
  videoSetup = 'Treadmill',
  overlayStyle = 'Medical',
  analysisQuality = 'Full',
  userHeight = { feet: 5, inches: 10 }
}) => {
  const [showAnglePanel, setShowAnglePanel] = useState(true);
  const [currentPoseData, setCurrentPoseData] = useState<any>(null);

  // CRITICAL: Map analysisQuality to modelQuality for 3D mode (will be intelligently overridden based on videoSetup)
  const modelQuality: 'Full' | 'Heavy' = analysisMode === '3D' ? (analysisQuality as 'Full' | 'Heavy') : 'Full';

  console.log('📊 ResultsPanel render:');
  console.log('analysisMode:', analysisMode);
  console.log('videoSetup:', videoSetup);
  console.log('analysisQuality (UI setting):', analysisQuality);
  console.log('modelQuality (mapped for BlazePose):', modelQuality);
  console.log('userHeight:', userHeight);
  
  if (analysisMode === '3D') {
    const isBackFacing = videoSetup?.toLowerCase().includes('out') || videoSetup?.toLowerCase().includes('offset');
    console.log('🤖 INTELLIGENT MODEL OVERRIDE PREVIEW:', {
      userSelected: modelQuality,
      videoSetup: videoSetup,
      isBackFacing: isBackFacing,
      actualModelUsed: isBackFacing ? 'Lite (auto-selected)' : `${modelQuality} (user preference honored)`,
      reason: isBackFacing ? 'Back-facing scenario detected' : 'Front-facing scenario'
    });
  }

  const handlePoseData = (data: any) => {
    setCurrentPoseData(data);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-3xl font-bold text-gray-900">
              Live {analysisMode} Running Analysis - {userHeight.feet}'{userHeight.inches}"
            </h1>
            {analysisMode === '3D' && (
              <button
                onClick={() => setShowAnglePanel(!showAnglePanel)}
                className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors"
              >
                {showAnglePanel ? 'Hide' : 'Show'} Angles
              </button>
            )}
          </div>
          <button
            onClick={onNewAnalysis}
            className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
          >
            New Analysis
          </button>
        </div>

        <div className="text-sm text-gray-600">
          Real-time {analysisMode} pose analysis with precise height-based scaling using user height ({userHeight.feet}'{userHeight.inches}") and known distances (Treadmill: 5ft, Out&In: 5-40ft).
        </div>

        {/* Angle Panel for 3D Analysis */}
        {analysisMode === '3D' && (
          <AnglePanel 
            poseData={currentPoseData}
            isVisible={showAnglePanel}
            onToggleVisibility={() => setShowAnglePanel(false)}
          />
        )}

        {/* Main Content */}
        <div className="grid lg:grid-cols-4 gap-6">
          {/* Video Players - Side by Side */}
          <div className="lg:col-span-3 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Side View Video */}
              {sideVideo && (
                <Card>
                  <CardContent className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Side View - Live {analysisMode} Overlay</h2>
                    <div className="w-full">
                      <VideoPlayer 
                        videoUrl={sideVideo.url} 
                        analysisType="running"
                        viewType="side"
                        analysisMode={analysisMode}
                        videoSetup={videoSetup}
                        overlayStyle={overlayStyle}
                        modelQuality={modelQuality}
                        userHeight={userHeight}
                        onPoseData={handlePoseData}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Rear View Video */}
              {rearVideo && (
                <Card>
                  <CardContent className="p-6">
                    <h2 className="text-xl font-semibold mb-4">Rear View - Live {analysisMode} Overlay</h2>
                    <div className="w-full">
                      <VideoPlayer 
                        videoUrl={rearVideo.url} 
                        analysisType="running"
                        viewType="rear"
                        analysisMode={analysisMode}
                        videoSetup={videoSetup}
                        overlayStyle={overlayStyle}
                        modelQuality={modelQuality}
                        userHeight={userHeight}
                        onPoseData={handlePoseData}
                      />
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>

          {/* Analysis Panel */}
          <div className="space-y-6">
            <PronationAnalysis />
          </div>
        </div>

        {/* Success Banner */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
              <div className="w-3 h-3 text-white">✓</div>
            </div>
            <span className="text-green-800 font-medium">
              Live {analysisMode} skeletal overlay with precise height-based scaling using user height ({userHeight.feet}'{userHeight.inches}") and known setup distances
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResultsPanel;
