
import React, { useRef, useEffect, useState } from 'react';
import { useMoveNetDetection } from '@/hooks/useMoveNetDetection';
import { 
  scaleKeypoints, 
  drawPoseConnections, 
  drawPoseKeypoints, 
  drawDebugFrame 
} from '@/utils/poseRenderer';
import { processPoseData } from '@/utils/poseCalculations';

interface CyclingPoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  onPoseData?: (data: any) => void;
  userHeight?: { feet: number; inches: number };
}

const CyclingPoseOverlay: React.FC<CyclingPoseOverlayProps> = ({ 
  videoRef, 
  onPoseData,
  userHeight = { feet: 5, inches: 10 }
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const [frameCount, setFrameCount] = useState(0);
  
  // SEALED 2D PIPELINE: Use dedicated MoveNet hook
  const { detector, isInitialized, debugInfo, setDebugInfo, detectPoses } = useMoveNetDetection();

  // Convert user height to meters for calculations
  const userHeightMeters = (userHeight.feet * 12 + userHeight.inches) * 0.0254;

  useEffect(() => {
    if (!isInitialized || !videoRef.current || !canvasRef.current) return;

    const animate = async () => {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      if (!video || !canvas) return;

      try {
        const rect = video.getBoundingClientRect();
        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Set canvas dimensions to match video
        if (canvas.width !== rect.width || canvas.height !== rect.height) {
          canvas.width = rect.width;
          canvas.height = rect.height;
          canvas.style.position = 'absolute';
          canvas.style.top = '0px';
          canvas.style.left = '0px';
          canvas.style.width = `${rect.width}px`;
          canvas.style.height = `${rect.height}px`;
          canvas.style.pointerEvents = 'none';
          canvas.style.zIndex = '10';
        }

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw debug frame
        drawDebugFrame(ctx, frameCount);
        
        // SEALED 2D PIPELINE: Use MoveNet detector ONLY
        const poses = await detectPoses(video);
        
        if (poses.length > 0 && poses[0].keypoints) {
          const convertedKeypoints = poses[0].keypoints.map((kp: any) => ({
            x: kp.x || 0,
            y: kp.y || 0,
            score: kp.score ?? 0
          }));
          
          // Scale keypoints with user height parameter
          const scaledKeypoints = scaleKeypoints(
            convertedKeypoints,
            rect.width,
            rect.height,
            video.videoWidth,
            video.videoHeight,
            userHeightMeters
          );
          
          // PHASE 1 & 2: Draw with enhanced visibility - cycling specific
          drawPoseConnections(ctx, scaledKeypoints);
          drawPoseKeypoints(ctx, scaledKeypoints);
          
          // Process pose data
          try {
            const poseData = processPoseData(poses, frameCount);
            if (poseData && onPoseData) {
              onPoseData({
                ...poseData,
                pipeline: '2D-MoveNet-Cycling',
                userHeight: userHeight
              });
            }
          } catch (processError) {
            console.error('❌ SEALED 2D PIPELINE: Cycling processing error:', processError);
          }
          
          setDebugInfo(`✓ SEALED 2D PIPELINE Cycling Frame ${frameCount}: ${poses[0].keypoints.length} keypoints - ${userHeight.feet}'${userHeight.inches}" - MoveNet ONLY`);
        } else {
          setDebugInfo(`SEALED 2D PIPELINE Cycling Frame ${frameCount}: No poses - ${userHeight.feet}'${userHeight.inches}" - MoveNet ONLY`);
        }
        
      } catch (error) {
        console.error('❌ SEALED 2D PIPELINE: Cycling pose detection error:', error);
        setDebugInfo(`2D Cycling Error: ${error.message}`);
      }
      
      setFrameCount(prev => prev + 1);
      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animationFrameRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isInitialized, videoRef, frameCount, onPoseData, detectPoses, setDebugInfo, userHeightMeters, userHeight]);

  return (
    <>
      <canvas ref={canvasRef} />
      <div 
        style={{
          position: 'absolute',
          top: '10px',
          right: '10px',
          background: 'rgba(0,100,200,0.8)',
          color: 'white',
          padding: '6px',
          borderRadius: '4px',
          fontSize: '11px',
          zIndex: 11
        }}
      >
        Cycling Analysis 2D
        <br />
        {userHeight.feet}'{userHeight.inches}" • MoveNet
        <br />
        Frame: {frameCount}
      </div>
    </>
  );
};

export default CyclingPoseOverlay;
