
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';

const PronationBreakdown: React.FC = () => {
  const pronationData = [
    {
      type: 'Underpronation',
      percentage: 25,
      color: 'bg-yellow-400',
      icon: '🦶',
      description: 'Foot rolls outward'
    },
    {
      type: 'Neutral Pronation',
      percentage: 50,
      color: 'bg-green-400',
      icon: '✓',
      description: 'Optimal foot landing'
    },
    {
      type: 'Overpronation',
      percentage: 25,
      color: 'bg-orange-400',
      icon: '⚠',
      description: 'Foot rolls inward'
    }
  ];

  return (
    <Card>
      <CardContent className="p-6">
        <h3 className="text-xl font-semibold mb-6">Pronation Breakdown</h3>
        
        <div className="space-y-6">
          {pronationData.map((item, index) => (
            <div key={index} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center text-lg">
                    {item.icon}
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{item.type}</div>
                    <div className="text-sm text-gray-600">{item.description}</div>
                  </div>
                </div>
                <div className="text-lg font-semibold text-gray-900">{item.percentage}%</div>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className={`h-full ${item.color} rounded-full transition-all duration-500`}
                  style={{ width: `${item.percentage}%` }}
                />
              </div>
            </div>
          ))}
        </div>

        {/* Summary Section */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">Analysis Summary</h4>
          <p className="text-sm text-blue-700">
            Your running form shows balanced pronation with 50% neutral foot landing. 
            Consider working on slightly reducing overpronation for optimal efficiency.
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default PronationBreakdown;
