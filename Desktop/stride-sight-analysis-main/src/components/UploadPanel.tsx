
import React, { useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Check, Upload, X, RefreshCw } from 'lucide-react';

interface VideoFile {
  file: File;
  url: string;
  name: string;
}

interface UploadPanelProps {
  title: string;
  description: string;
  acceptedFormats: string;
  video: VideoFile | null;
  onVideoUpload: (video: VideoFile | null) => void;
}

const UploadPanel: React.FC<UploadPanelProps> = ({
  title,
  description,
  acceptedFormats,
  video,
  onVideoUpload,
}) => {
  const handleFileSelect = useCallback((file: File) => {
    console.log('📁 File selected:', file.name, 'Size:', (file.size / 1024 / 1024).toFixed(2) + 'MB', 'Type:', file.type);
    
    if (!file) {
      console.error('❌ No file provided');
      return;
    }
    
    if (file.size > 100 * 1024 * 1024) { // 100MB limit
      console.error('❌ File too large:', (file.size / 1024 / 1024).toFixed(2) + 'MB');
      alert('File is too large. Please select a video under 100MB.');
      return;
    }
    
    try {
      const url = URL.createObjectURL(file);
      console.log('✅ File processed successfully, created URL:', url);
      onVideoUpload({
        file,
        url,
        name: file.name,
      });
    } catch (error) {
      console.error('❌ Error creating object URL:', error);
      alert('Error processing file. Please try again.');
    }
  }, [onVideoUpload]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    console.log('🎯 File dropped');
    const files = Array.from(e.dataTransfer.files);
    console.log('📄 Files dropped:', files.map(f => ({ name: f.name, type: f.type, size: f.size })));
    
    const videoFile = files.find(file => 
      file.type.startsWith('video/') || 
      ['mp4', 'mov', 'avi', 'webm'].some(ext => file.name.toLowerCase().endsWith(ext))
    );
    
    if (videoFile) {
      console.log('✅ Valid video file found:', videoFile.name);
      handleFileSelect(videoFile);
    } else {
      console.error('❌ No valid video file found in dropped files');
      alert('Please drop a valid video file (MP4, MOV, AVI, or WebM)');
    }
  }, [handleFileSelect]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    console.log('🖱️ File input triggered');
    const file = e.target.files?.[0];
    console.log('📁 File from input:', file ? { name: file.name, type: file.type, size: file.size } : 'No file');
    
    if (file) {
      handleFileSelect(file);
    } else {
      console.warn('⚠️ No file selected from input');
    }
  }, [handleFileSelect]);

  const handleRemove = () => {
    if (video) {
      URL.revokeObjectURL(video.url);
      onVideoUpload(null);
    }
  };

  const handleReplace = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'video/*,.mp4,.mov,.avi,.webm';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        if (video) {
          URL.revokeObjectURL(video.url);
        }
        handleFileSelect(file);
      }
    };
    input.click();
  };

  return (
    <Card className="hover:shadow-lg transition-shadow">
      <CardContent className="p-6">
        <div className="text-center space-y-4">
          <Upload className="w-12 h-12 text-blue-500 mx-auto" />
          <div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">{title}</h3>
            <p className="text-gray-600 mb-4">{description}</p>
          </div>

          {!video ? (
            <div
              onDrop={handleDrop}
              onDragOver={(e) => {
                e.preventDefault();
                console.log('📤 Drag over detected');
              }}
              onDragEnter={(e) => {
                e.preventDefault();
                console.log('📥 Drag enter detected');
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                console.log('📤 Drag leave detected');
              }}
              className="border-2 border-dashed border-gray-300 rounded-lg p-8 hover:border-blue-400 hover:bg-blue-50 transition-colors cursor-pointer"
            >
              <div className="space-y-4">
                <div className="text-gray-500">{acceptedFormats}</div>
                <label className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg cursor-pointer transition-colors">
                  <Upload className="w-5 h-5" />
                  Choose File
                  <input
                    type="file"
                    className="hidden"
                    accept="video/*,.mp4,.mov,.avi,.webm"
                    onChange={handleFileInput}
                  />
                </label>
                <div className="text-sm text-gray-500">or drag and drop here</div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-center gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
                <Check className="w-6 h-6 text-green-600" />
                <span className="text-green-800 font-medium">{video.name}</span>
              </div>
              <div className="flex justify-center gap-3">
                <button
                  onClick={handleReplace}
                  className="inline-flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  Replace
                </button>
                <button
                  onClick={handleRemove}
                  className="inline-flex items-center gap-2 px-4 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50 transition-colors"
                >
                  <X className="w-4 h-4" />
                  Remove
                </button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default UploadPanel;
