
import React, { useRef, useEffect } from 'react';
import { useMoveNetDetection } from '@/hooks/useMoveNetDetection';
import { 
  scaleKeypoints, 
  drawPoseConnections, 
  drawPoseKeypoints, 
  drawDebugFrame 
} from '@/utils/poseRenderer';
import { processPoseData } from '@/utils/poseCalculations';

interface RearViewPoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: any) => void;
}

const RearViewPoseOverlay: React.FC<RearViewPoseOverlayProps> = ({ 
  videoRef, 
  userHeight = { feet: 5, inches: 10 },
  onPoseData 
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  
  // SEALED 2D PIPELINE: Use dedicated MoveNet hook
  const { detector, isInitialized, debugInfo, setDebugInfo, detectPoses } = useMoveNetDetection();

  // Convert user height to meters for calculations
  const userHeightMeters = (userHeight.feet * 12 + userHeight.inches) * 0.0254;

  useEffect(() => {
    // Rear view component uses MoveNet for 2D detection
    console.log('👤 User height:', `${userHeight.feet}'${userHeight.inches}"`, `(${userHeightMeters.toFixed(2)}m)`);
    
    if (animationFrameRef.current) {
      console.log('🛑 Canceling previous animation frame');
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (!isInitialized || !videoRef.current || !canvasRef.current) {
      // MoveNet not ready yet
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      console.error('❌ Failed to get 2D context from canvas');
      return;
    }

    // Starting rear view MoveNet detection
    let frameCount = 0;

    const detectPose = async () => {
      try {
        if (!video || video.paused || video.ended) {
          console.log('⏹️ Video stopped, ending rear view pose detection');
          return;
        }

        frameCount++;
        
        if (frameCount % 30 === 0) {
          // Throttled frame logging
          if (frameCount % 60 === 0) {
            console.log(`🏃 Rear View Frame ${frameCount}`);
          }
        }

        // Wait for video to be ready
        if (video.videoWidth === 0 || video.videoHeight === 0 || video.readyState < 2) {
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        const rect = video.getBoundingClientRect();
        
        if (rect.width === 0 || rect.height === 0) {
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // Set canvas size and position
        if (canvas.width !== rect.width || canvas.height !== rect.height) {
          canvas.width = rect.width;
          canvas.height = rect.height;
          canvas.style.position = 'absolute';
          canvas.style.top = '0px';
          canvas.style.left = '0px';
          canvas.style.width = `${rect.width}px`;
          canvas.style.height = `${rect.height}px`;
          canvas.style.pointerEvents = 'none';
          canvas.style.zIndex = '10';
        }

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // Draw debug frame
        drawDebugFrame(ctx, frameCount);
        
        // SEALED 2D PIPELINE: Use MoveNet detector ONLY
        const poses = await detectPoses(video);
        
        if (frameCount % 60 === 0) {
          console.log(`🏃 MoveNet detected ${poses.length} poses (frame ${frameCount})`);
        }
        
        if (poses.length > 0 && poses[0].keypoints) {
          // Convert TensorFlow keypoints to our PoseKeypoint format
          const convertedKeypoints = poses[0].keypoints.map((kp: any) => ({
            x: kp.x || 0,
            y: kp.y || 0,
            score: kp.score ?? 0
          }));
          
          // Scale keypoints with precise height-based scaling (treadmill = 5ft distance)
          const scaledKeypoints = scaleKeypoints(
            convertedKeypoints,
            rect.width,
            rect.height,
            video.videoWidth,
            video.videoHeight,
            userHeightMeters
          );
          
          // Draw pose with rear view features (no primary/secondary styling)
          try {
            drawPoseConnections(ctx, scaledKeypoints, 'rear');
            drawPoseKeypoints(ctx, scaledKeypoints, 'rear');
            
            // Add angle annotations for rear view
            const { drawAngleAnnotations } = await import('@/utils/poseRenderer');
            drawAngleAnnotations(ctx, scaledKeypoints);
            
          } catch (drawError) {
            console.error('❌ SEALED 2D PIPELINE: Rear view drawing error:', drawError);
          }
          
          // Process pose data
          try {
            const poseData = processPoseData(poses, frameCount);
            if (poseData && onPoseData) {
              onPoseData({
                ...poseData,
                pipeline: '2D-MoveNet-ONLY',
                userHeight: userHeight
              });
            }
          } catch (processError) {
            console.error('❌ SEALED 2D PIPELINE: Processing error:', processError);
          }
          
          setDebugInfo(`✓ SEALED 2D PIPELINE Frame ${frameCount}: ${poses[0].keypoints.length} keypoints - ${userHeight.feet}'${userHeight.inches}" - MoveNet ONLY`);
        } else {
          setDebugInfo(`SEALED 2D PIPELINE Frame ${frameCount}: No poses - ${userHeight.feet}'${userHeight.inches}" - MoveNet ONLY`);
        }
        
      } catch (error) {
        console.error('❌ SEALED 2D PIPELINE: Pose detection error:', error);
        setDebugInfo(`2D Error: ${error.message}`);
      }

      // Continue animation loop if video is still playing
      if (video && !video.paused && !video.ended) {
        animationFrameRef.current = requestAnimationFrame(detectPose);
      }
    };

    // Start detection with a small delay
    const startTimeout = setTimeout(() => {
      detectPose();
    }, 100);

    return () => {
      // Cleaning up rear view pose detection
      clearTimeout(startTimeout);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [isInitialized, videoRef, detectPoses, setDebugInfo, onPoseData, userHeight, userHeightMeters]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10
      }}
    />
  );
};

export default RearViewPoseOverlay;
