import React from 'react';
import SideViewPoseOverlay from './SideViewPoseOverlay';
import RearViewPoseOverlay from './RearViewPoseOverlay';
import CyclingPoseOverlay from './CyclingPoseOverlay';
import BlazePoseOverlay from './BlazePoseOverlay';

interface PoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  analysisType?: 'running' | 'cycling';
  analysisMode?: '2D' | '3D';
  viewType?: 'side' | 'rear';
  overlayStyle?: string;
  modelQuality?: 'Full' | 'Heavy';
  videoSetup?: string;
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: any) => void;
}

const PoseOverlay: React.FC<PoseOverlayProps> = ({ 
  videoRef, 
  analysisType = 'running',
  analysisMode = '2D',
  viewType = 'side',
  overlayStyle = 'Medical',
  modelQuality = 'Full',
  videoSetup = 'Treadmill',
  userHeight = { feet: 5, inches: 10 },
  onPoseData 
}) => {
  console.log('=== SEALED PIPELINE AUDIT ===');
  console.log('🔒 STRICT SEALED PIPELINE SEPARATION:');
  console.log('analysisMode:', analysisMode, 'type:', typeof analysisMode);
  if (analysisMode === '2D') {
    console.log('✅ SEALED 2D PIPELINE: Will use MoveNet ONLY - NO BlazePose');
  } else if (analysisMode === '3D') {
    console.log('✅ SEALED 3D PIPELINE: Will use BlazePose ONLY - NO MoveNet');
  }
  console.log('analysisType:', analysisType, 'type:', typeof analysisType);
  console.log('viewType:', viewType, 'type:', typeof viewType);
  console.log('videoSetup:', videoSetup, 'type:', typeof videoSetup);
  console.log('modelQuality:', modelQuality, 'type:', typeof modelQuality);
  console.log('overlayStyle:', overlayStyle, 'type:', typeof overlayStyle);
  console.log('userHeight:', userHeight);
  console.log('==========================================');
  
  // SAFETY: 3D MODE COMPLETELY DISABLED FOR SYSTEM STABILITY
  if (analysisMode === '3D') {
    console.error('🚨 CRITICAL SAFETY: 3D mode blocked - this should never happen!');
    console.error('🚨 SAFETY: Forcing fallback to 2D mode for system stability');
    console.error('🚨 SAFETY: BlazePose 3D pipeline disabled to prevent system crashes');
    
    // Force fallback to 2D mode - treat as if analysisMode was '2D'
    // This is a safety net that should never be reached
  }

  // SEALED 2D PIPELINE: Only reach here for 2D mode - MoveNet ONLY
  console.log('📍 SEALED 2D PIPELINE: Using 2D overlays for analysisMode:', analysisMode);
  console.log('✅ SEALED 2D PIPELINE: MoveNet ONLY - NO BlazePose crossover');
  console.log('2D overlay selection - analysisType:', analysisType, 'viewType:', viewType);
  
  const renderOverlay = () => {
    // SEALED 2D PIPELINE: All 2D overlays use MoveNet ONLY
    if (analysisType === 'cycling') {
      console.log('SEALED 2D PIPELINE: Using CyclingPoseOverlay - MoveNet ONLY');
      return <CyclingPoseOverlay videoRef={videoRef} onPoseData={onPoseData} />;
    }
    
    // For running analysis, choose based on view type
    if (viewType === 'rear') {
      console.log('SEALED 2D PIPELINE: Using RearViewPoseOverlay - MoveNet ONLY');
      return <RearViewPoseOverlay videoRef={videoRef} userHeight={userHeight} onPoseData={onPoseData} />;
    }
    
    // Default to side view with overlay style support
    console.log('SEALED 2D PIPELINE: Using SideViewPoseOverlay with style:', overlayStyle, '- MoveNet ONLY');
    return (
      <SideViewPoseOverlay 
        videoRef={videoRef} 
        onPoseData={onPoseData}
        overlayStyle={overlayStyle}
        userHeight={userHeight}
      />
    );
  };

  return (
    <>
      {renderOverlay()}
      <DebugInfo 
        analysisMode={analysisMode} 
        viewType={viewType} 
        overlayStyle={overlayStyle}
        modelQuality={modelQuality}
        videoSetup={videoSetup}
        userHeight={userHeight}
      />
    </>
  );
};

const DebugInfo: React.FC<{ 
  analysisMode?: string; 
  viewType?: string; 
  overlayStyle?: string;
  modelQuality?: 'Full' | 'Heavy';
  videoSetup?: string;
  userHeight?: { feet: number; inches: number };
}> = ({ 
  analysisMode = '2D',
  viewType = 'side', 
  overlayStyle = 'Medical',
  modelQuality = 'Full',
  videoSetup = 'Treadmill',
  userHeight = { feet: 5, inches: 10 }
}) => {
  const getDebugText = () => {
    if (analysisMode === '3D') {
      const setupDisplay = videoSetup === 'OffsetOutAndIn' ? 'Offset Out and In' : 
                          videoSetup === 'OutAndIn' ? 'Out and In' : 
                          videoSetup === 'Treadmill' ? 'Treadmill' : videoSetup;
      return `🔒 SEALED 3D PIPELINE - BLAZEPOSE ONLY (${modelQuality}) - ${setupDisplay} (5ft) - ${userHeight.feet}'${userHeight.inches}"`;
    }
    return `🔒 SEALED 2D PIPELINE - ${viewType} view (${overlayStyle}) - MOVENET ONLY - ${userHeight.feet}'${userHeight.inches}"`;
  };

  return (
    <div 
      style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: analysisMode === '3D' ? 'cyan' : 'lime',
        padding: '8px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 11
      }}
    >
      DEBUG: {getDebugText()}
    </div>
  );
};

export default PoseOverlay;
