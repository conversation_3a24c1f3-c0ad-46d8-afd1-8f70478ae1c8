import React from 'react';
import SideViewPoseOverlay from './SideViewPoseOverlay';
import RearViewPoseOverlay from './RearViewPoseOverlay';
import CyclingPoseOverlay from './CyclingPoseOverlay';
import BlazePoseOverlay from './BlazePoseOverlay';

interface PoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  analysisType?: 'running' | 'cycling';
  analysisMode?: '2D' | '3D';
  viewType?: 'side' | 'rear';
  overlayStyle?: string;
  modelQuality?: 'Full' | 'Heavy';
  videoSetup?: string;
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: any) => void;
}

const PoseOverlay: React.FC<PoseOverlayProps> = ({ 
  videoRef, 
  analysisType = 'running',
  analysisMode = '2D',
  viewType = 'side',
  overlayStyle = 'Medical',
  modelQuality = 'Full',
  videoSetup = 'Treadmill',
  userHeight = { feet: 5, inches: 10 },
  onPoseData 
}) => {
  console.log('=== SEALED PIPELINE AUDIT ===');
  console.log('🔒 STRICT SEALED PIPELINE SEPARATION:');
  console.log('analysisMode:', analysisMode, 'type:', typeof analysisMode);
  if (analysisMode === '2D') {
    console.log('✅ SEALED 2D PIPELINE: Will use MoveNet ONLY - NO BlazePose');
  } else if (analysisMode === '3D') {
    console.log('✅ SEALED 3D PIPELINE: Will use BlazePose ONLY - NO MoveNet');
  }
  console.log('analysisType:', analysisType, 'type:', typeof analysisType);
  console.log('viewType:', viewType, 'type:', typeof viewType);
  console.log('videoSetup:', videoSetup, 'type:', typeof videoSetup);
  console.log('modelQuality:', modelQuality, 'type:', typeof modelQuality);
  console.log('overlayStyle:', overlayStyle, 'type:', typeof overlayStyle);
  console.log('userHeight:', userHeight);
  console.log('==========================================');
  
  // SECTION 1: Enable 3D pipeline with intelligent fallback (Phase 3 activation)
  const [pipelineMode, setPipelineMode] = useState<'3D' | '2D' | 'auto'>('auto');
  const [pipelineStatus, setPipelineStatus] = useState<'initializing' | 'ready' | 'fallback' | 'error'>('initializing');

  // Check if 3D mode is requested and system supports it
  const shouldUse3D = analysisMode === '3D' && pipelineMode !== '2D';

  if (analysisMode === '3D') {
    console.log('🎯 SECTION 1: 3D mode requested - checking system capability');

    // System capability check will be handled by the detection hook
    // No longer blocking 3D mode - let the pipeline handle fallbacks
  }

  // SECTION 1: Intelligent pipeline routing with 3D support
  console.log('📍 SECTION 1: Pipeline routing - analysisMode:', analysisMode, 'shouldUse3D:', shouldUse3D);

  const renderOverlay = () => {
    // SECTION 1: Route to 3D pipeline if requested and supported
    if (shouldUse3D) {
      console.log('🎯 SECTION 1: Using 3D BlazePose pipeline');
      // For now, use the side view overlay with 3D detection
      // TODO: Create dedicated 3D overlay components in future phases
      return (
        <SideViewPoseOverlay
          videoRef={videoRef}
          onPoseData={onPoseData}
          overlayStyle={overlayStyle}
          userHeight={userHeight}
          use3D={true}
          pipelineStatus={pipelineStatus}
          onPipelineStatusChange={setPipelineStatus}
        />
      );
    }

    // SEALED 2D PIPELINE: All 2D overlays use MoveNet ONLY
    console.log('📍 SEALED 2D PIPELINE: Using 2D overlays for analysisMode:', analysisMode);

    if (analysisType === 'cycling') {
      console.log('SEALED 2D PIPELINE: Using CyclingPoseOverlay - MoveNet ONLY');
      return <CyclingPoseOverlay videoRef={videoRef} onPoseData={onPoseData} />;
    }

    // For running analysis, choose based on view type
    if (viewType === 'rear') {
      console.log('SEALED 2D PIPELINE: Using RearViewPoseOverlay - MoveNet ONLY');
      return <RearViewPoseOverlay videoRef={videoRef} userHeight={userHeight} onPoseData={onPoseData} />;
    }

    // Default to side view with overlay style support
    console.log('SEALED 2D PIPELINE: Using SideViewPoseOverlay with style:', overlayStyle, '- MoveNet ONLY');
    return (
      <SideViewPoseOverlay
        videoRef={videoRef}
        onPoseData={onPoseData}
        overlayStyle={overlayStyle}
        userHeight={userHeight}
        use3D={false}
      />
    );
  };

  return (
    <>
      {renderOverlay()}
      <DebugInfo 
        analysisMode={analysisMode} 
        viewType={viewType} 
        overlayStyle={overlayStyle}
        modelQuality={modelQuality}
        videoSetup={videoSetup}
        userHeight={userHeight}
      />
    </>
  );
};

const DebugInfo: React.FC<{ 
  analysisMode?: string; 
  viewType?: string; 
  overlayStyle?: string;
  modelQuality?: 'Full' | 'Heavy';
  videoSetup?: string;
  userHeight?: { feet: number; inches: number };
}> = ({ 
  analysisMode = '2D',
  viewType = 'side', 
  overlayStyle = 'Medical',
  modelQuality = 'Full',
  videoSetup = 'Treadmill',
  userHeight = { feet: 5, inches: 10 }
}) => {
  const getDebugText = () => {
    if (analysisMode === '3D') {
      const setupDisplay = videoSetup === 'OffsetOutAndIn' ? 'Offset Out and In' : 
                          videoSetup === 'OutAndIn' ? 'Out and In' : 
                          videoSetup === 'Treadmill' ? 'Treadmill' : videoSetup;
      return `🔒 SEALED 3D PIPELINE - BLAZEPOSE ONLY (${modelQuality}) - ${setupDisplay} (5ft) - ${userHeight.feet}'${userHeight.inches}"`;
    }
    return `🔒 SEALED 2D PIPELINE - ${viewType} view (${overlayStyle}) - MOVENET ONLY - ${userHeight.feet}'${userHeight.inches}"`;
  };

  return (
    <div 
      style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        background: 'rgba(0,0,0,0.8)',
        color: analysisMode === '3D' ? 'cyan' : 'lime',
        padding: '8px',
        borderRadius: '4px',
        fontSize: '12px',
        zIndex: 11
      }}
    >
      DEBUG: {getDebugText()}
    </div>
  );
};

export default PoseOverlay;
