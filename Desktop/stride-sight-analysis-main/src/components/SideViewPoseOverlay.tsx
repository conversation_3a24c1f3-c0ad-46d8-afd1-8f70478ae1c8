
import React, { useRef, useEffect, useState } from 'react';
import { useMoveNetDetection } from '@/hooks/useMoveNetDetection';
import { 
  scaleKeypoints, 
  drawPoseConnections, 
  drawPoseKeypoints,
  drawElbowAngles,
  drawPostureAnalysis
} from '@/utils/poseRenderer';
import { processPoseData } from '@/utils/poseCalculations';

interface SideViewPoseOverlayProps {
  videoRef: React.RefObject<HTMLVideoElement>;
  onPoseData?: (data: any) => void;
  overlayStyle?: string;
  userHeight?: { feet: number; inches: number };
}

const SideViewPoseOverlay: React.FC<SideViewPoseOverlayProps> = ({ 
  videoRef, 
  onPoseData,
  overlayStyle = 'Medical',
  userHeight = { feet: 5, inches: 10 }
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number | null>(null);
  const [frameCount, setFrameCount] = useState(0);
  
  // SEALED 2D PIPELINE: Use dedicated MoveNet hook
  const { isInitialized, setDebugInfo, detectPoses } = useMoveNetDetection();

  // Convert user height to meters for calculations
  const userHeightMeters = (userHeight.feet * 12 + userHeight.inches) * 0.0254;

  useEffect(() => {
    // Side view component uses MoveNet for 2D detection
    console.log('🎨 Overlay Style:', overlayStyle);
    console.log('👤 User height:', `${userHeight.feet}'${userHeight.inches}"`, `(${userHeightMeters.toFixed(2)}m)`);

    if (animationFrameRef.current) {
      console.log('🛑 Canceling previous animation frame');
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    if (!isInitialized || !videoRef.current || !canvasRef.current) {
      // MoveNet not ready yet
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      console.error('❌ Failed to get 2D context from canvas');
      return;
    }

    // ANTI-FLICKER: Track last video time to prevent duplicate processing
    let lastVideoTime = -1;
    let isProcessing = false;

    // Starting side view MoveNet detection

    const detectPose = async () => {
      try {
        if (!video || video.paused || video.ended) {
          return;
        }

        // REFINED: Enhanced anti-flicker with time threshold for smoother playback
        const currentVideoTime = video.currentTime;
        const timeThreshold = 0.001; // Minimum time difference to process new frame

        if (isProcessing || Math.abs(currentVideoTime - lastVideoTime) < timeThreshold) {
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        isProcessing = true;
        lastVideoTime = currentVideoTime;

        setFrameCount(prev => prev + 1);
        const currentFrame = frameCount + 1;

        if (currentFrame % 30 === 0) {
          if (currentFrame % 60 === 0) {
            console.log(`🏃 Side View Frame ${currentFrame} (time: ${currentVideoTime.toFixed(2)}s)`);
          }
        }

        // Wait for video to be ready
        if (video.videoWidth === 0 || video.videoHeight === 0 || video.readyState < 2) {
          isProcessing = false;
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        const rect = video.getBoundingClientRect();

        if (rect.width === 0 || rect.height === 0) {
          isProcessing = false;
          animationFrameRef.current = requestAnimationFrame(detectPose);
          return;
        }

        // Set canvas size and position (only when needed to prevent flicker)
        if (canvas.width !== rect.width || canvas.height !== rect.height) {
          canvas.width = rect.width;
          canvas.height = rect.height;
          canvas.style.position = 'absolute';
          canvas.style.top = '0px';
          canvas.style.left = '0px';
          canvas.style.width = `${rect.width}px`;
          canvas.style.height = `${rect.height}px`;
          canvas.style.pointerEvents = 'none';
          canvas.style.zIndex = '10';
        }

        // SMOOTH CLEAR: Use fillRect with transparent color for smoother clearing
        ctx.save();
        ctx.globalCompositeOperation = 'source-over';
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        // SEALED 2D PIPELINE: Use MoveNet detector ONLY
        const poses = await detectPoses(video);
        
        if (poses.length > 0 && poses[0].keypoints) {
          const convertedKeypoints = poses[0].keypoints.map((kp: any) => ({
            x: kp.x || 0,
            y: kp.y || 0,
            score: kp.score ?? 0
          }));
          
          // Scale keypoints with user height parameter
          const scaledKeypoints = scaleKeypoints(
            convertedKeypoints,
            rect.width,
            rect.height,
            video.videoWidth,
            video.videoHeight,
            userHeightMeters
          );
          
          // MEDICAL-GRADE SKELETON: Enhanced rendering with heel/foot estimation
          console.log('🏥 MEDICAL SKELETON: Rendering enhanced side view with clinical precision');
          
          // 1. Draw medical-grade skeleton connections (includes heel/foot estimation)
          drawPoseConnections(ctx, scaledKeypoints);
          
          // 2. Draw medical-grade keypoints (no eyes, priority-based sizing)
          drawPoseKeypoints(ctx, scaledKeypoints);
          
          // 3. Draw elbow angle arcs with medical annotations
          drawElbowAngles(ctx, scaledKeypoints);
          
          // 4. Draw posture analysis with lean detection
          drawPostureAnalysis(ctx, scaledKeypoints, canvas.width, canvas.height);
          
          // Process pose data
          try {
            const poseData = processPoseData(poses, currentFrame);
            if (poseData && onPoseData) {
              onPoseData({
                ...poseData,
                pipeline: '2D-MoveNet-ONLY',
                userHeight: userHeight
              });
            }
          } catch (processError) {
            console.error('❌ SEALED 2D PIPELINE: Processing error:', processError);
          }
          
          setDebugInfo(`✓ SEALED 2D PIPELINE Frame ${currentFrame}: ${poses[0].keypoints.length} keypoints - ${userHeight.feet}'${userHeight.inches}" - MoveNet ONLY`);
        } else {
          setDebugInfo(`SEALED 2D PIPELINE Frame ${currentFrame}: No poses - ${userHeight.feet}'${userHeight.inches}" - MoveNet ONLY`);
        }

        ctx.restore();
        isProcessing = false;

      } catch (error) {
        console.error('❌ SEALED 2D PIPELINE: Pose detection error:', error);
        setDebugInfo(`2D Error: ${error.message}`);
        ctx.restore();
        isProcessing = false;
      }

      // Continue animation loop if video is still playing
      if (video && !video.paused && !video.ended) {
        animationFrameRef.current = requestAnimationFrame(detectPose);
      }
    };

    // Start detection with a small delay
    const startTimeout = setTimeout(() => {
      detectPose();
    }, 100);

    return () => {
      // Cleaning up side view pose detection
      clearTimeout(startTimeout);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
    };
  }, [isInitialized, videoRef, detectPoses, setDebugInfo, onPoseData, overlayStyle, userHeight, userHeightMeters, frameCount]);

  return (
    <canvas
      ref={canvasRef}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
        zIndex: 10
      }}
    />
  );
};

export default SideViewPoseOverlay;
