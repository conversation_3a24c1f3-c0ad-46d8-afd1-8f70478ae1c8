/**
 * SECTION 2: Comprehensive Unit Test Suite for BlazePose Pipeline
 * Tests all Phase 1 and Phase 2 functionality with validation
 */

import * as tf from '@tensorflow/tfjs-core';
import { calculateLandmarkProjection } from '../shared/calculators/calculate_landmark_projection';
import { calculateWorldLandmarkProjection } from '../shared/calculators/calculate_world_landmark_projection';
import { normalizedKeypointsToKeypoints } from '../shared/calculators/normalized_keypoints_to_keypoints';
import { removeLandmarkLetterbox } from '../shared/calculators/remove_landmark_letterbox';
import { nonMaxSuppression } from '../shared/calculators/non_max_suppression';
import { removeDetectionLetterbox } from '../shared/calculators/remove_detection_letterbox';
import { convertImageToTensor, validateImageQuality } from '../shared/calculators/convert_image_to_tensor';
import { Keypoint, Detection, ImageSize } from '../shared/calculators/interfaces/common_interfaces';
import { Rect, Padding } from '../shared/calculators/interfaces/shape_interfaces';

export interface TestResult {
  testName: string;
  passed: boolean;
  error?: string;
  duration: number;
  details?: any;
}

export class BlazePoseUnitTests {
  private results: TestResult[] = [];

  /**
   * SECTION 2: Run all unit tests and return results
   */
  async runAllTests(): Promise<{ passed: number; failed: number; results: TestResult[] }> {
    console.log('🧪 SECTION 2: Starting comprehensive unit test suite');
    
    this.results = [];
    
    // Phase 1 coordinate transformation function tests
    await this.testCalculateLandmarkProjection();
    await this.testCalculateWorldLandmarkProjection();
    await this.testNormalizedKeypointsToKeypoints();
    await this.testRemoveLandmarkLetterbox();
    
    // Phase 2 detection pipeline tests
    await this.testNonMaxSuppression();
    await this.testRemoveDetectionLetterbox();
    
    // Phase 2 image processing tests
    await this.testConvertImageToTensor();
    await this.testValidateImageQuality();
    
    // Error handling and edge case tests
    await this.testErrorHandling();
    await this.testEdgeCases();
    
    const passed = this.results.filter(r => r.passed).length;
    const failed = this.results.filter(r => !r.passed).length;
    
    console.log(`🧪 SECTION 2: Unit tests complete - ${passed} passed, ${failed} failed`);
    
    return { passed, failed, results: this.results };
  }

  /**
   * SECTION 2: Test calculateLandmarkProjection function
   */
  private async testCalculateLandmarkProjection(): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Test data
      const landmarks: Keypoint[] = [
        { x: 0.5, y: 0.5, score: 0.9, name: 'test_landmark' },
        { x: 0.0, y: 0.0, score: 0.8, name: 'corner_landmark' },
        { x: 1.0, y: 1.0, score: 0.7, name: 'opposite_corner' }
      ];
      
      const rect: Rect = {
        xCenter: 0.5,
        yCenter: 0.5,
        width: 0.8,
        height: 0.8
      };
      
      const result = calculateLandmarkProjection(landmarks, rect);
      
      // Validate results
      if (!result || result.length !== landmarks.length) {
        throw new Error('Invalid result length');
      }
      
      // Check that coordinates are properly transformed
      const firstResult = result[0];
      if (isNaN(firstResult.x) || isNaN(firstResult.y) || isNaN(firstResult.score)) {
        throw new Error('NaN values in result');
      }
      
      // Check coordinate bounds
      if (firstResult.x < 0 || firstResult.x > 1 || firstResult.y < 0 || firstResult.y > 1) {
        throw new Error('Coordinates out of bounds');
      }
      
      this.results.push({
        testName: 'calculateLandmarkProjection',
        passed: true,
        duration: performance.now() - startTime,
        details: { inputCount: landmarks.length, outputCount: result.length }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'calculateLandmarkProjection',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test calculateWorldLandmarkProjection function
   */
  private async testCalculateWorldLandmarkProjection(): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Test data with 3D coordinates
      const worldLandmarks: Keypoint[] = [
        { x: 0.0, y: 0.0, z: 0.0, score: 0.9, name: 'world_landmark_1' },
        { x: 0.5, y: 0.5, z: -0.1, score: 0.8, name: 'world_landmark_2' },
        { x: -0.2, y: 0.3, z: 0.2, score: 0.7, name: 'world_landmark_3' }
      ];
      
      const rect: Rect = {
        xCenter: 0.5,
        yCenter: 0.5,
        width: 1.0,
        height: 1.0
      };
      
      const result = calculateWorldLandmarkProjection(worldLandmarks, rect);
      
      // Validate results
      if (!result || result.length !== worldLandmarks.length) {
        throw new Error('Invalid result length');
      }
      
      // Check 3D coordinates
      const firstResult = result[0];
      if (isNaN(firstResult.x) || isNaN(firstResult.y) || isNaN(firstResult.z || 0)) {
        throw new Error('NaN values in 3D result');
      }
      
      this.results.push({
        testName: 'calculateWorldLandmarkProjection',
        passed: true,
        duration: performance.now() - startTime,
        details: { inputCount: worldLandmarks.length, outputCount: result.length }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'calculateWorldLandmarkProjection',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test normalizedKeypointsToKeypoints function
   */
  private async testNormalizedKeypointsToKeypoints(): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Test data with normalized coordinates
      const normalizedKeypoints: Keypoint[] = [
        { x: 0.5, y: 0.5, score: 0.9, name: 'center_point' },
        { x: 0.0, y: 0.0, score: 0.8, name: 'top_left' },
        { x: 1.0, y: 1.0, score: 0.7, name: 'bottom_right' }
      ];
      
      const imageSize: ImageSize = { width: 640, height: 480 };
      
      const result = normalizedKeypointsToKeypoints(normalizedKeypoints, imageSize);
      
      // Validate results
      if (!result || result.length !== normalizedKeypoints.length) {
        throw new Error('Invalid result length');
      }
      
      // Check coordinate scaling
      const centerPoint = result[0];
      const expectedX = 0.5 * imageSize.width;
      const expectedY = 0.5 * imageSize.height;
      
      if (Math.abs(centerPoint.x - expectedX) > 1 || Math.abs(centerPoint.y - expectedY) > 1) {
        throw new Error('Coordinate scaling incorrect');
      }
      
      this.results.push({
        testName: 'normalizedKeypointsToKeypoints',
        passed: true,
        duration: performance.now() - startTime,
        details: { imageSize, scaledCoordinates: result[0] }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'normalizedKeypointsToKeypoints',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test removeLandmarkLetterbox function
   */
  private async testRemoveLandmarkLetterbox(): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Test data with letterboxed coordinates
      const landmarks: Keypoint[] = [
        { x: 320, y: 240, score: 0.9, name: 'center_landmark' },
        { x: 160, y: 120, score: 0.8, name: 'offset_landmark' }
      ];
      
      const padding: Padding = { top: 60, bottom: 60, left: 80, right: 80 };
      const originalSize: ImageSize = { width: 640, height: 480 };
      
      const result = removeLandmarkLetterbox(landmarks, padding, originalSize);
      
      // Validate results
      if (!result || result.length !== landmarks.length) {
        throw new Error('Invalid result length');
      }
      
      // Check padding removal
      const centerResult = result[0];
      if (isNaN(centerResult.x) || isNaN(centerResult.y)) {
        throw new Error('NaN values after letterbox removal');
      }
      
      this.results.push({
        testName: 'removeLandmarkLetterbox',
        passed: true,
        duration: performance.now() - startTime,
        details: { padding, originalSize, resultCount: result.length }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'removeLandmarkLetterbox',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test nonMaxSuppression function
   */
  private async testNonMaxSuppression(): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Test data with overlapping detections
      const detections: Detection[] = [
        {
          boundingBox: { xMin: 100, yMin: 100, width: 200, height: 200 },
          score: 0.9,
          landmarks: []
        },
        {
          boundingBox: { xMin: 150, yMin: 150, width: 200, height: 200 },
          score: 0.7,
          landmarks: []
        },
        {
          boundingBox: { xMin: 400, yMin: 400, width: 100, height: 100 },
          score: 0.8,
          landmarks: []
        }
      ];
      
      const config = {
        maxDetections: 2,
        iouThreshold: 0.3,
        scoreThreshold: 0.5
      };
      
      const result = nonMaxSuppression(detections, config);
      
      // Validate results
      if (!result || result.length > config.maxDetections) {
        throw new Error('Invalid result count');
      }
      
      // Check that highest scoring detection is kept
      if (result.length > 0 && result[0].score !== 0.9) {
        throw new Error('Highest scoring detection not preserved');
      }
      
      this.results.push({
        testName: 'nonMaxSuppression',
        passed: true,
        duration: performance.now() - startTime,
        details: { inputCount: detections.length, outputCount: result.length }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'nonMaxSuppression',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test removeDetectionLetterbox function
   */
  private async testRemoveDetectionLetterbox(): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Test data with letterboxed detections
      const detections: Detection[] = [
        {
          boundingBox: { xMin: 160, yMin: 120, width: 320, height: 240 },
          score: 0.9,
          landmarks: []
        }
      ];
      
      const padding: Padding = { top: 60, bottom: 60, left: 80, right: 80 };
      const originalSize: ImageSize = { width: 640, height: 480 };
      
      const result = removeDetectionLetterbox(detections, padding, originalSize);
      
      // Validate results
      if (!result || result.length !== detections.length) {
        throw new Error('Invalid result length');
      }
      
      // Check coordinate adjustment
      const adjustedDetection = result[0];
      if (isNaN(adjustedDetection.boundingBox.xMin) || isNaN(adjustedDetection.boundingBox.yMin)) {
        throw new Error('NaN values in adjusted detection');
      }
      
      this.results.push({
        testName: 'removeDetectionLetterbox',
        passed: true,
        duration: performance.now() - startTime,
        details: { padding, originalSize, resultCount: result.length }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'removeDetectionLetterbox',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test convertImageToTensor function
   */
  private async testConvertImageToTensor(): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Create test canvas
      const canvas = document.createElement('canvas');
      canvas.width = 640;
      canvas.height = 480;
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Cannot create canvas context');
      
      // Fill with test pattern
      ctx.fillStyle = 'rgb(128, 128, 128)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      const targetSize: ImageSize = { width: 256, height: 256 };
      
      const result = convertImageToTensor(canvas, targetSize, true);
      
      // Validate tensor
      if (!result || result.shape.length !== 3) {
        throw new Error('Invalid tensor shape');
      }
      
      if (result.shape[0] !== targetSize.height || result.shape[1] !== targetSize.width) {
        throw new Error('Incorrect tensor dimensions');
      }
      
      // Check tensor values
      const data = await result.data();
      const avgValue = data.reduce((sum, val) => sum + val, 0) / data.length;
      
      if (isNaN(avgValue) || avgValue < 0 || avgValue > 1) {
        throw new Error('Invalid tensor values');
      }
      
      result.dispose();
      
      this.results.push({
        testName: 'convertImageToTensor',
        passed: true,
        duration: performance.now() - startTime,
        details: { targetSize, tensorShape: result.shape, avgValue }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'convertImageToTensor',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test validateImageQuality function
   */
  private async testValidateImageQuality(): Promise<void> {
    const startTime = performance.now();
    
    try {
      // Create test canvas with known quality
      const canvas = document.createElement('canvas');
      canvas.width = 320;
      canvas.height = 240;
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Cannot create canvas context');
      
      // Create medium brightness, good contrast image
      ctx.fillStyle = 'rgb(100, 100, 100)';
      ctx.fillRect(0, 0, canvas.width / 2, canvas.height);
      ctx.fillStyle = 'rgb(200, 200, 200)';
      ctx.fillRect(canvas.width / 2, 0, canvas.width / 2, canvas.height);
      
      const result = validateImageQuality(canvas);
      
      // Validate quality assessment
      if (typeof result.isValid !== 'boolean' || !Array.isArray(result.issues)) {
        throw new Error('Invalid quality result structure');
      }
      
      this.results.push({
        testName: 'validateImageQuality',
        passed: true,
        duration: performance.now() - startTime,
        details: { isValid: result.isValid, issueCount: result.issues.length }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'validateImageQuality',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test error handling capabilities
   */
  private async testErrorHandling(): Promise<void> {
    const startTime = performance.now();
    
    try {
      let errorsCaught = 0;
      
      // Test invalid inputs
      try {
        calculateLandmarkProjection([], { xCenter: 0.5, yCenter: 0.5, width: 0.8, height: 0.8 });
      } catch {
        errorsCaught++;
      }
      
      try {
        normalizedKeypointsToKeypoints([], { width: 0, height: 0 });
      } catch {
        errorsCaught++;
      }
      
      try {
        nonMaxSuppression([], { maxDetections: 1, iouThreshold: 0.3, scoreThreshold: 0.5 });
      } catch {
        // This should not throw an error for empty array
      }
      
      this.results.push({
        testName: 'errorHandling',
        passed: true,
        duration: performance.now() - startTime,
        details: { errorsCaught }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'errorHandling',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }

  /**
   * SECTION 2: Test edge cases and boundary conditions
   */
  private async testEdgeCases(): Promise<void> {
    const startTime = performance.now();
    
    try {
      let edgeCasesPassed = 0;
      
      // Test NaN coordinates
      const nanLandmarks: Keypoint[] = [
        { x: NaN, y: 0.5, score: 0.9, name: 'nan_landmark' }
      ];
      
      const rect: Rect = { xCenter: 0.5, yCenter: 0.5, width: 0.8, height: 0.8 };
      const result = calculateLandmarkProjection(nanLandmarks, rect);
      
      // Should handle NaN gracefully
      if (result.length > 0 && !isNaN(result[0].x)) {
        edgeCasesPassed++;
      }
      
      // Test extreme coordinates
      const extremeLandmarks: Keypoint[] = [
        { x: -10, y: 10, score: 0.9, name: 'extreme_landmark' }
      ];
      
      const extremeResult = calculateLandmarkProjection(extremeLandmarks, rect);
      if (extremeResult.length > 0) {
        edgeCasesPassed++;
      }
      
      this.results.push({
        testName: 'edgeCases',
        passed: edgeCasesPassed >= 2,
        duration: performance.now() - startTime,
        details: { edgeCasesPassed }
      });
      
    } catch (error) {
      this.results.push({
        testName: 'edgeCases',
        passed: false,
        error: error instanceof Error ? error.message : String(error),
        duration: performance.now() - startTime
      });
    }
  }
}
