/**
 * SECTION 2: Comprehensive Test Runner for BlazePose Pipeline
 * Executes all unit tests, integration tests, and performance benchmarks
 */

import { BlazePoseUnitTests, TestResult } from './blazepose-unit-tests';
import { BlazePoseIntegrationTests, IntegrationTestResult } from './blazepose-integration-tests';
import { BlazePosePerformanceTests, PerformanceBenchmark } from './blazepose-performance-tests';

export interface TestSuiteResults {
  unitTests: {
    passed: number;
    failed: number;
    results: TestResult[];
  };
  integrationTests: {
    passed: number;
    failed: number;
    results: IntegrationTestResult[];
  };
  performanceBenchmarks: {
    passed: number;
    failed: number;
    benchmarks: PerformanceBenchmark[];
  };
  overall: {
    totalPassed: number;
    totalFailed: number;
    successRate: number;
    duration: number;
  };
}

export class BlazePoseTestRunner {
  private unitTests: BlazePoseUnitTests;
  private integrationTests: BlazePoseIntegrationTests;
  private performanceTests: BlazePosePerformanceTests;

  constructor() {
    this.unitTests = new BlazePoseUnitTests();
    this.integrationTests = new BlazePoseIntegrationTests();
    this.performanceTests = new BlazePosePerformanceTests();
  }

  /**
   * SECTION 2: Run complete test suite
   */
  async runCompleteTestSuite(): Promise<TestSuiteResults> {
    console.log('🧪 SECTION 2: Starting complete BlazePose test suite');
    const startTime = performance.now();

    try {
      // Run unit tests
      console.log('📋 SECTION 2: Running unit tests...');
      const unitResults = await this.unitTests.runAllTests();
      console.log(`✅ SECTION 2: Unit tests complete - ${unitResults.passed}/${unitResults.passed + unitResults.failed} passed`);

      // Run integration tests
      console.log('🔗 SECTION 2: Running integration tests...');
      const integrationResults = await this.integrationTests.runAllTests();
      console.log(`✅ SECTION 2: Integration tests complete - ${integrationResults.passed}/${integrationResults.passed + integrationResults.failed} passed`);

      // Run performance benchmarks
      console.log('⚡ SECTION 2: Running performance benchmarks...');
      const performanceResults = await this.performanceTests.runAllBenchmarks();
      console.log(`✅ SECTION 2: Performance benchmarks complete - ${performanceResults.passed}/${performanceResults.passed + performanceResults.failed} passed`);

      const duration = performance.now() - startTime;
      const totalPassed = unitResults.passed + integrationResults.passed + performanceResults.passed;
      const totalFailed = unitResults.failed + integrationResults.failed + performanceResults.failed;
      const successRate = (totalPassed / (totalPassed + totalFailed)) * 100;

      const results: TestSuiteResults = {
        unitTests: unitResults,
        integrationTests: integrationResults,
        performanceBenchmarks: performanceResults,
        overall: {
          totalPassed,
          totalFailed,
          successRate,
          duration
        }
      };

      console.log('🎯 SECTION 2: Complete test suite finished');
      console.log(`📊 SECTION 2: Overall results - ${totalPassed}/${totalPassed + totalFailed} passed (${successRate.toFixed(1)}%)`);
      console.log(`⏱️ SECTION 2: Total duration: ${duration.toFixed(1)}ms`);

      return results;

    } catch (error) {
      console.error('❌ SECTION 2: Test suite execution failed:', error);
      throw error;
    }
  }

  /**
   * SECTION 2: Generate detailed test report
   */
  generateTestReport(results: TestSuiteResults): string {
    const report = `
# BlazePose Phase 3 Test Suite Report

## Executive Summary
- **Total Tests**: ${results.overall.totalPassed + results.overall.totalFailed}
- **Passed**: ${results.overall.totalPassed}
- **Failed**: ${results.overall.totalFailed}
- **Success Rate**: ${results.overall.successRate.toFixed(1)}%
- **Duration**: ${results.overall.duration.toFixed(1)}ms

## Unit Tests Results
- **Passed**: ${results.unitTests.passed}
- **Failed**: ${results.unitTests.failed}

### Unit Test Details
${results.unitTests.results.map(test => `
- **${test.testName}**: ${test.passed ? '✅ PASSED' : '❌ FAILED'} (${test.duration.toFixed(1)}ms)
  ${test.error ? `  Error: ${test.error}` : ''}
  ${test.details ? `  Details: ${JSON.stringify(test.details)}` : ''}
`).join('')}

## Integration Tests Results
- **Passed**: ${results.integrationTests.passed}
- **Failed**: ${results.integrationTests.failed}

### Integration Test Details
${results.integrationTests.results.map(test => `
- **${test.testName}**: ${test.passed ? '✅ PASSED' : '❌ FAILED'} (${test.duration.toFixed(1)}ms)
  ${test.error ? `  Error: ${test.error}` : ''}
  ${test.memoryUsage ? `  Memory: ${test.memoryUsage.leaked} tensors leaked` : ''}
  ${test.performanceMetrics ? `  Performance: ${test.performanceMetrics.processingTime.toFixed(1)}ms avg` : ''}
`).join('')}

## Performance Benchmarks Results
- **Passed**: ${results.performanceBenchmarks.passed}
- **Failed**: ${results.performanceBenchmarks.failed}

### Performance Benchmark Details
${results.performanceBenchmarks.benchmarks.map(benchmark => `
- **${benchmark.testName}**: ${benchmark.passed ? '✅ PASSED' : '❌ FAILED'}
  - Processing Time: ${benchmark.current.processingTime.toFixed(1)}ms (${benchmark.improvement.processingTime > 0 ? '+' : ''}${benchmark.improvement.processingTime.toFixed(1)}% vs baseline)
  - Memory Usage: ${benchmark.current.memoryUsage.toFixed(1)}MB (${benchmark.improvement.memoryUsage > 0 ? '+' : ''}${benchmark.improvement.memoryUsage.toFixed(1)}% vs baseline)
  - FPS: ${benchmark.current.framesPerSecond.toFixed(1)} (${benchmark.improvement.framesPerSecond > 0 ? '+' : ''}${benchmark.improvement.framesPerSecond.toFixed(1)}% vs baseline)
`).join('')}

## Recommendations

### Critical Issues
${results.overall.successRate < 90 ? '⚠️ Success rate below 90% - investigate failed tests before production deployment' : '✅ Success rate acceptable for production deployment'}

### Performance Analysis
${results.performanceBenchmarks.benchmarks.filter(b => !b.passed).length > 0 ? 
  '⚠️ Performance benchmarks failed - review optimization strategies' : 
  '✅ Performance benchmarks passed - system ready for production'}

### Memory Management
${results.integrationTests.results.some(t => t.memoryUsage && t.memoryUsage.leaked > 20) ? 
  '⚠️ Memory leaks detected - review tensor disposal and cleanup' : 
  '✅ Memory management stable - no significant leaks detected'}

## Conclusion
${results.overall.successRate >= 95 ? 
  '🎯 **READY FOR PRODUCTION**: All critical tests passed, system is stable and performant' :
  results.overall.successRate >= 85 ?
  '⚠️ **NEEDS ATTENTION**: Most tests passed but some issues need resolution before production' :
  '❌ **NOT READY**: Significant issues detected, requires investigation and fixes'}
`;

    return report;
  }

  /**
   * SECTION 2: Run specific test category
   */
  async runUnitTestsOnly(): Promise<{ passed: number; failed: number; results: TestResult[] }> {
    console.log('📋 SECTION 2: Running unit tests only...');
    return await this.unitTests.runAllTests();
  }

  async runIntegrationTestsOnly(): Promise<{ passed: number; failed: number; results: IntegrationTestResult[] }> {
    console.log('🔗 SECTION 2: Running integration tests only...');
    return await this.integrationTests.runAllTests();
  }

  async runPerformanceTestsOnly(): Promise<{ passed: number; failed: number; benchmarks: PerformanceBenchmark[] }> {
    console.log('⚡ SECTION 2: Running performance tests only...');
    return await this.performanceTests.runAllBenchmarks();
  }

  /**
   * SECTION 2: Validate system readiness for production
   */
  async validateProductionReadiness(): Promise<{
    ready: boolean;
    issues: string[];
    recommendations: string[];
    testResults: TestSuiteResults;
  }> {
    console.log('🔍 SECTION 2: Validating production readiness...');

    const testResults = await this.runCompleteTestSuite();
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check success rate
    if (testResults.overall.successRate < 95) {
      issues.push(`Low success rate: ${testResults.overall.successRate.toFixed(1)}% (target: 95%+)`);
      recommendations.push('Investigate and fix failed tests before production deployment');
    }

    // Check unit tests
    if (testResults.unitTests.failed > 0) {
      issues.push(`${testResults.unitTests.failed} unit tests failed`);
      recommendations.push('Fix unit test failures - these indicate core functionality issues');
    }

    // Check integration tests
    if (testResults.integrationTests.failed > 2) {
      issues.push(`${testResults.integrationTests.failed} integration tests failed`);
      recommendations.push('Review integration test failures - may indicate system stability issues');
    }

    // Check performance benchmarks
    const criticalPerformanceFailures = testResults.performanceBenchmarks.benchmarks.filter(
      b => !b.passed && (b.testName === 'memoryEfficiency' || b.testName === 'extendedOperation')
    );
    
    if (criticalPerformanceFailures.length > 0) {
      issues.push('Critical performance benchmarks failed');
      recommendations.push('Address memory management and stability issues before production');
    }

    // Check memory leaks
    const memoryLeaks = testResults.integrationTests.results.filter(
      t => t.memoryUsage && t.memoryUsage.leaked > 20
    );
    
    if (memoryLeaks.length > 0) {
      issues.push('Memory leaks detected in integration tests');
      recommendations.push('Review tensor disposal and implement better memory cleanup');
    }

    const ready = issues.length === 0 && testResults.overall.successRate >= 95;

    console.log(`🎯 SECTION 2: Production readiness: ${ready ? 'READY' : 'NOT READY'}`);
    if (issues.length > 0) {
      console.log('⚠️ SECTION 2: Issues found:', issues);
    }

    return {
      ready,
      issues,
      recommendations,
      testResults
    };
  }
}

// Export singleton instance for easy use
export const blazePoseTestRunner = new BlazePoseTestRunner();
