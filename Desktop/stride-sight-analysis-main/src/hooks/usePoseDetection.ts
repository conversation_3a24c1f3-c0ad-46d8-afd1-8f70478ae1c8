
// DEPRECATED: This file has been replaced by sealed pipeline hooks
// Use useMoveNetDetection.ts for 2D analysis
// Use useBlazePoseDetection.ts for 3D analysis

console.warn('⚠️ DEPRECATED: usePoseDetection.ts should not be used. Use sealed pipeline hooks instead.');

export const usePoseDetection = () => {
  console.error('❌ DEPRECATED HOOK: usePoseDetection is deprecated. Use dedicated hooks:');
  console.error('📍 For 2D analysis: import { useMoveNetDetection } from "./useMoveNetDetection"');
  console.error('📍 For 3D analysis: import { useBlazePoseDetection } from "./useBlazePoseDetection"');
  
  return {
    detector: null,
    blazePoseDetector: null,
    isInitialized: false,
    isBlazePoseInitialized: false,
    debugInfo: 'DEPRECATED - Use sealed pipeline hooks',
    setDebugInfo: () => {},
    detectPoses: () => Promise.resolve([])
  };
};
