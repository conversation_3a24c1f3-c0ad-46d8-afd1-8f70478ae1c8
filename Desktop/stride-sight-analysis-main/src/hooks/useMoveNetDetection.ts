
import { useState, useEffect, useRef } from 'react';
import * as tf from '@tensorflow/tfjs';
import * as poseDetection from '@tensorflow-models/pose-detection';

export const useMoveNetDetection = () => {
  const [detector, setDetector] = useState<poseDetection.PoseDetector | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  
  // PHASE 3: Performance tracking for MoveNet
  const frameCount = useRef<number>(0);
  const lastProcessingTime = useRef<number>(0);
  const averageProcessingTime = useRef<number>(0);
  
  const shouldLog = (frequency: number = 60): boolean => {
    return frameCount.current % frequency === 0;
  };

  useEffect(() => {
    const initMoveNet = async () => {
      console.log('🔄 SEALED 2D PIPELINE: Initializing MoveNet ONLY...');
      try {
        await tf.ready();
        console.log('✅ TensorFlow.js ready for MoveNet (2D)');
        
        const model = poseDetection.SupportedModels.MoveNet;
        const detectorConfig = {
          modelType: poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING,
        };
        
        const moveNetDetector = await poseDetection.createDetector(model, detectorConfig);
        setDetector(moveNetDetector);
        setIsInitialized(true);
        console.log('✅ SEALED 2D PIPELINE: MoveNet detector initialized successfully');
        console.log('❌ SEALED 2D PIPELINE: NO BlazePose allowed in this hook');
        
        setDebugInfo('2D Pipeline: MoveNet ONLY - Initialized');
      } catch (error) {
        console.error('❌ SEALED 2D PIPELINE: MoveNet initialization failed:', error);
        setDebugInfo(`2D MoveNet Init Error: ${error.message}`);
      }
    };

    initMoveNet();
  }, []);

  const detectPoses = async (video: HTMLVideoElement) => {
    const startTime = performance.now();
    frameCount.current++;
    
    if (shouldLog(300)) {
      console.log('🔍 SEALED 2D PIPELINE: Running MoveNet pose detection ONLY');
    }
    
    if (!detector || !video) {
      if (shouldLog(300)) {
        console.log('❌ SEALED 2D PIPELINE: Missing MoveNet detector or video');
      }
      return [];
    }

    try {
      if (shouldLog(180)) {
        console.log('🚀 SEALED 2D PIPELINE: MoveNet estimating poses...');
      }
      
      const poses = await detector.estimatePoses(video);
      
      // PHASE 3: Track MoveNet performance
      const processingTime = performance.now() - startTime;
      lastProcessingTime.current = processingTime;
      
      // Update rolling average
      if (averageProcessingTime.current === 0) {
        averageProcessingTime.current = processingTime;
      } else {
        averageProcessingTime.current = averageProcessingTime.current * 0.9 + processingTime * 0.1;
      }
      
      if (shouldLog(120)) {
        console.log(`✅ MoveNet: ${poses.length} poses in ${processingTime.toFixed(1)}ms (avg: ${averageProcessingTime.current.toFixed(1)}ms)`);
      }
      
      return poses;
    } catch (error) {
      console.error('❌ SEALED 2D PIPELINE: MoveNet detection error:', error);
      setDebugInfo(`2D MoveNet Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return [];
    }
  };

  return {
    detector,
    isInitialized,
    debugInfo,
    setDebugInfo,
    detectPoses
  };
};
