
import { useState, useEffect, useRef } from 'react';
import * as tf from '@tensorflow/tfjs';
import * as poseDetection from '@tensorflow-models/pose-detection';
import { getImageSize } from '../shared/calculators/image_utils';
import { KeypointsSmoothingFilter } from '../shared/filters/keypoints_smoothing';
import { LowPassVisibilityFilter } from '../shared/filters/visibility_smoothing';
import { PoseStabilityFilter } from '../shared/filters/pose_stability_filter';
import { debugBlazePoseStructure, debugTensorMemory, blazePoseHealthCheck } from '../shared/calculators/blazepose_debug_utils';
import { safeTensorDispose, validateTensor, filterNaNValues, filterNaNValues2D, keypointsToTensor2D } from '../shared/calculators/tensor_utils';
import { tensorsToLandmarks, TensorsToLandmarksConfig } from '../shared/calculators/tensors_to_landmarks';
import { refineLandmarksFromHeatmap, RefineLandmarksFromHeatmapConfig } from '../shared/calculators/refine_landmarks_from_heatmap';
import { validateAndCleanPoseCoordinates } from '../shared/calculators/blazepose_tensor_processor';
import { TensorPipelineTest } from '../shared/calculators/tensor_pipeline_test';
// SECTION 1: Raw Tensor Processing Pipeline Imports
import { convertImageToTensor, preprocessImageForBlazePose, preprocessImageForDetection, validateImageQuality, ImageProcessingConfig } from '../shared/calculators/convert_image_to_tensor';
import { tensorsToDetections, TensorsToDetectionsConfig } from '../shared/calculators/tensors_to_detections';
import { detectorResult, DetectorResult } from '../shared/calculators/detector_result';
import { createSsdAnchors, SsdAnchorConfig, Anchor } from '../shared/calculators/create_ssd_anchors';
import { nonMaxSuppression, NonMaxSuppressionConfig } from '../shared/calculators/non_max_suppression';
import { calculateLandmarkProjection } from '../shared/calculators/calculate_landmark_projection';
import { calculateWorldLandmarkProjection, calculateWorldLandmarkProjectionFromTensor } from '../shared/calculators/calculate_world_landmark_projection';
import { normalizedKeypointsToKeypoints } from '../shared/calculators/normalized_keypoints_to_keypoints';
import { removeLandmarkLetterbox, calculateLetterboxPadding } from '../shared/calculators/remove_landmark_letterbox';
import { removeDetectionLetterbox, calculateDetectionLetterboxPadding, validateDetectionBounds } from '../shared/calculators/remove_detection_letterbox';
import { calculateROIFromLandmarks } from '../shared/calculators/roi_processing';
import { Detection, Keypoint, BoundingBox, ImageSize } from '../shared/calculators/interfaces/common_interfaces';
import { Rect, Padding } from '../shared/calculators/interfaces/shape_interfaces';

// SECTION 1: Configuration Constants for Raw Tensor Processing
const BLAZEPOSE_DETECTION_CONFIG: TensorsToDetectionsConfig = {
  numClasses: 1,
  numBoxes: 896,
  numCoords: 12,
  boxCoordOffset: 0,
  keypointCoordOffset: 4,
  numKeypoints: 4,
  numValuesPerKeypoint: 2,
  sigmoidScore: true,
  scoreClippingThresh: 100.0,
  reverseOutputOrder: false,
  xScale: 128.0,
  yScale: 128.0,
  wScale: 128.0,
  hScale: 128.0,
  minScoreThresh: 0.5
};

const BLAZEPOSE_LANDMARK_CONFIG: TensorsToLandmarksConfig = {
  numLandmarks: 33,
  inputImageWidth: 256,
  inputImageHeight: 256,
  normalizeZ: 1.0,
  visibilityActivation: 'sigmoid',
  flipHorizontally: false,
  flipVertically: false
};

const BLAZEPOSE_WORLD_LANDMARK_CONFIG: TensorsToLandmarksConfig = {
  numLandmarks: 33,
  inputImageWidth: 256,
  inputImageHeight: 256,
  normalizeZ: 1.0,
  visibilityActivation: 'none',
  flipHorizontally: false,
  flipVertically: false
};

const BLAZEPOSE_REFINEMENT_CONFIG: RefineLandmarksFromHeatmapConfig = {
  kernelSize: 7,
  minConfidenceToRefine: 0.5
};

const BLAZEPOSE_NMS_CONFIG: NonMaxSuppressionConfig = {
  maxDetections: 1,
  iouThreshold: 0.3,
  scoreThreshold: 0.5
};

// SECTION 1: Enhanced NMS configuration with soft NMS support
const BLAZEPOSE_NMS_CONFIG_ENHANCED: NonMaxSuppressionConfig = {
  maxDetections: 1,
  iouThreshold: 0.3,
  scoreThreshold: 0.5,
  softNmsEnabled: false,
  softNmsSigma: 0.5
};

const BLAZEPOSE_ANCHOR_CONFIG: SsdAnchorConfig = {
  inputSizeWidth: 128,
  inputSizeHeight: 128,
  minScale: 0.1484375,
  maxScale: 0.75,
  anchorOffsetX: 0.5,
  anchorOffsetY: 0.5,
  numLayers: 4,
  featureMapWidth: [16, 8, 4, 2],
  featureMapHeight: [16, 8, 4, 2],
  strides: [8, 16, 32, 64],
  aspectRatios: [1.0],
  reduceBoxesInLowestLayer: false,
  interpolatedScaleAspectRatio: 1.0,
  fixedAnchorSize: true
};

// SECTION 3: 3D Coordinate Processing Configuration
const BLAZEPOSE_WORLD_LANDMARK_CONFIG_ENHANCED: WorldLandmarkConfig = {
  normalizeZ: 1.0,
  worldCoordinateScale: 1.0,
  numWorldLandmarks: 39,
  coordinatesPerLandmark: 3,
  zCoordinateThreshold: 0.1,
  worldCoordinateRange: [-2.0, 2.0] as [number, number]
};

// SECTION 3: Complete Configuration Constants for Phase 2
const BLAZEPOSE_DETECTION_LETTERBOX_CONFIG = {
  preserveAspectRatio: true,
  paddingColor: [0, 0, 0] as [number, number, number],
  interpolationMethod: 'bilinear' as 'bilinear' | 'nearest',
  centerCrop: false
};

const BLAZEPOSE_IMAGE_PROCESSING_CONFIG = {
  detectionInputSize: { width: 128, height: 128 },
  landmarkInputSize: { width: 256, height: 256 },
  normalizationRange: [0, 1] as [number, number],
  qualityThresholds: { minBrightness: 0.1, maxBrightness: 0.9 }
};

const BLAZEPOSE_PERFORMANCE_CONFIG = {
  enableTensorCaching: true,
  maxCachedTensors: 5,
  enableMemoryMonitoring: true,
  memoryCleanupInterval: 1000, // ms
  processingTimeoutMs: 100
};

interface WorldLandmarkConfig {
  normalizeZ: number;
  worldCoordinateScale: number;
  numWorldLandmarks: number;
  coordinatesPerLandmark: number;
  zCoordinateThreshold: number;
  worldCoordinateRange: [number, number];
}

export const useBlazePoseDetection = (modelQuality: 'Full' | 'Heavy' = 'Full', videoSetup?: string, userHeight?: { feet: number; inches: number }) => {
  const [detector, setDetector] = useState<poseDetection.PoseDetector | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [debugInfo, setDebugInfo] = useState<string>('');
  
  // PHASE 4 ENHANCED: Disable custom tensor processing pipeline by default (CRASH FIX)
  const [enableEnhancedTensorProcessing, setEnableEnhancedTensorProcessing] = useState(false);

  // SECTION 3: Performance monitoring state
  const [performanceMetrics, setPerformanceMetrics] = useState({
    averageProcessingTime: 0,
    frameCount: 0,
    memoryUsage: 0,
    tensorCount: 0
  });

  // SECTION 3: Tensor caching for performance optimization
  const tensorCacheRef = useRef<Map<string, tf.Tensor>>(new Map());
  const lastCleanupTimeRef = useRef<number>(Date.now());
  
  // PHASE 4: Enhanced Smoothing and Filtering Pipeline
  const [keypointsFilter] = useState(() => new KeypointsSmoothingFilter({
    frequency: 30,
    minCutOff: 1,
    beta: 0.007,
    derivateCutOff: 1,
    thresholdCutOff: 0.001,
    thresholdBeta: 0.1,
    disableValueScaling: true,
    velocitySmoothing: 0.15,
    accelerationDamping: 0.1
  }));
  
  const [visibilityFilter] = useState(() => new LowPassVisibilityFilter(0.1, 0.3, true));
  const [stabilityFilter] = useState(() => new PoseStabilityFilter({
    stabilityThreshold: 0.75,
    minStableFrames: 3,
    maxInstabilityFrames: 8,
    positionTolerance: 40.0
  }));
  
  // Add repair attempt tracking to prevent infinite loops
  const repairAttempts = useRef<number>(0);
  const lastRepairTime = useRef<number>(0);
  const frameCount = useRef<number>(0);
  const MAX_REPAIR_ATTEMPTS = 3;
  
  // PHASE 3: Performance optimization state
  const lastProcessingTime = useRef<number>(0);
  const averageProcessingTime = useRef<number>(0);
  const performanceMode = useRef<'realtime' | 'balanced' | 'quality'>('balanced');
  const frameSkipCounter = useRef<number>(0);
  const lastPoseResult = useRef<poseDetection.Pose[]>([]);
  const performanceHistory = useRef<number[]>([]);
  
  // PHASE 3: Memory management state
  const tensorMemoryThreshold = useRef<number>(500 * 1024 * 1024); // 500MB threshold
  const lastMemoryCleanup = useRef<number>(0);
  const memoryCleanupInterval = useRef<number>(5000); // 5 seconds initial value
  
  // Track if we're currently processing to prevent concurrent processing
  const isProcessing = useRef<boolean>(false);
  
  // Error tracking for CPU fallback
  const errorCount = useRef<number>(0);
  const lastErrorTime = useRef<number>(0);
  const cpuFallbackActive = useRef<boolean>(false);
  
  // Global logging throttle to reduce console spam - CRASH FIX: Much less frequent logging
  const shouldLog = (frequency: number = 300): boolean => {
    return frameCount.current % frequency === 0;
  };

  // CPU fallback mechanism for WebGL stability issues
  const forceUseCPU = async (): Promise<void> => {
    if (cpuFallbackActive.current) return;
    
    try {
      console.log('🔄 FORCING CPU BACKEND for stability');
      cpuFallbackActive.current = true;
      
      await tf.setBackend('cpu');
      await tf.ready();
      
      // Adjust memory settings for CPU backend
      tensorMemoryThreshold.current = 200 * 1024 * 1024; // 200MB for CPU
      memoryCleanupInterval.current = 10000; // 10 seconds
      
      console.log('✅ Successfully switched to CPU backend for stability');
    } catch (fallbackError) {
      console.error('❌ CPU fallback failed:', fallbackError);
    }
  };
  
  // PHASE 3: Enhanced adaptive performance monitoring with memory awareness
  const updatePerformanceMetrics = (processingTime: number) => {
    lastProcessingTime.current = processingTime;
    
    // Update rolling average (exponential moving average)
    if (averageProcessingTime.current === 0) {
      averageProcessingTime.current = processingTime;
    } else {
      // Use different weights based on performance trend
      const weight = processingTime > averageProcessingTime.current * 1.5 
        ? 0.3  // Weight outliers more heavily (react faster to slowdowns)
        : 0.1; // Normal weight for stable performance
      
      averageProcessingTime.current = averageProcessingTime.current * (1 - weight) + processingTime * weight;
    }
    
    // Get current memory info for adaptive decision making
    const memInfo = tf.memory();
    const currentMemoryUsage = memInfo.numBytes;
    const memoryPressure = currentMemoryUsage / tensorMemoryThreshold.current;
    
    // Adaptive performance mode based on processing time AND memory pressure
    if (memoryPressure > 0.8 || averageProcessingTime.current > 60) {
      // High memory pressure or very slow processing - most aggressive skipping
      performanceMode.current = 'quality';
      // Consider forcing a memory cleanup
      if (Date.now() - lastMemoryCleanup.current > 1000) { // At least 1 second since last cleanup
        // Schedule an immediate cleanup
        setTimeout(() => checkAndCleanupMemory(), 0);
      }
    } else if (averageProcessingTime.current > 50) { // > 50ms per frame
      performanceMode.current = 'quality'; // Skip frames for quality
    } else if (averageProcessingTime.current > 33 || memoryPressure > 0.6) { // > 33ms per frame (30fps) or moderate memory pressure
      performanceMode.current = 'balanced'; // Moderate skipping
    } else {
      performanceMode.current = 'realtime'; // Process every frame
    }
    
    // Track performance over time to detect degradation
    if (frameCount.current % 30 === 0) { // Sample every 30 frames
      performanceHistory.current.push(averageProcessingTime.current);
      // Keep only the last 10 samples
      if (performanceHistory.current.length > 10) {
        performanceHistory.current.shift();
      }
      
      // Check for performance degradation trend
      if (performanceHistory.current.length >= 5) {
        const firstHalf = performanceHistory.current.slice(0, Math.floor(performanceHistory.current.length / 2));
        const secondHalf = performanceHistory.current.slice(Math.floor(performanceHistory.current.length / 2));
        
        const firstHalfAvg = firstHalf.reduce((sum, time) => sum + time, 0) / firstHalf.length;
        const secondHalfAvg = secondHalf.reduce((sum, time) => sum + time, 0) / secondHalf.length;
        
        // If performance is degrading significantly (>20% slower)
        if (secondHalfAvg > firstHalfAvg * 1.2) {
          console.warn(`⚠️ PERFORMANCE DEGRADATION DETECTED: ${firstHalfAvg.toFixed(1)}ms → ${secondHalfAvg.toFixed(1)}ms`);
          
          // Force memory cleanup and consider resetting detector
          checkAndCleanupMemory();
          
          // Clear history after taking action
          performanceHistory.current = [];
        }
      }
    }
    
    if (shouldLog(120)) { // Log every 2 seconds
      console.log(`⚡ PERFORMANCE: ${processingTime.toFixed(1)}ms/frame, avg: ${averageProcessingTime.current.toFixed(1)}ms, mode: ${performanceMode.current}, memory: ${(memoryPressure * 100).toFixed(1)}%`);
    }
  };
  
  // PHASE 3: Enhanced smart frame skipping with adaptive patterns
  const shouldSkipFrame = (): boolean => {
    frameSkipCounter.current++;
    
    // Get current memory info for more adaptive decision making
    const memInfo = tf.memory();
    const currentMemoryUsage = memInfo.numBytes;
    const memoryPressure = currentMemoryUsage / tensorMemoryThreshold.current;
    
    // Extreme memory pressure - override normal patterns
    if (memoryPressure > 0.9) {
      // Under extreme memory pressure, process only every 4th frame
      // and force a cleanup on the next cycle
      if (Date.now() - lastMemoryCleanup.current > 500) { // At least 0.5 seconds since last cleanup
        setTimeout(() => checkAndCleanupMemory(), 0);
      }
      return frameSkipCounter.current % 4 !== 0;
    }
    
    // Dynamic skipping based on performance mode
    switch (performanceMode.current) {
      case 'realtime':
        // Even in realtime mode, skip frames under moderate memory pressure
        if (memoryPressure > 0.7) {
          return frameSkipCounter.current % 2 !== 0; // Skip every other frame
        }
        return false; // Process every frame
        
      case 'balanced':
        // In balanced mode, adapt based on memory pressure
        if (memoryPressure > 0.7) {
          return frameSkipCounter.current % 3 !== 0; // Process every 3rd frame
        }
        return frameSkipCounter.current % 2 === 0; // Skip every other frame
        
      case 'quality':
        // In quality mode, be even more aggressive with skipping
        if (memoryPressure > 0.7) {
          return frameSkipCounter.current % 4 !== 0; // Process every 4th frame
        }
        return frameSkipCounter.current % 3 !== 0; // Process every 3rd frame
        
      default:
        return false;
    }
  };
  
  // PHASE 3: Enhanced memory management and cleanup
  const checkAndCleanupMemory = async (): Promise<void> => {
    const now = Date.now();
    const timeSinceLastCleanup = now - lastMemoryCleanup.current;
    
    if (timeSinceLastCleanup < memoryCleanupInterval.current) {
      return; // Not time for cleanup yet
    }
    
    try {
      const memInfo = tf.memory();
      const currentMemoryUsage = memInfo.numBytes;
      const numTensors = memInfo.numTensors;
      
      // Adaptive memory threshold based on device capabilities
      // Adjust threshold based on current usage patterns
      if (numTensors > 100 || currentMemoryUsage > tensorMemoryThreshold.current * 0.8) {
        // Aggressive cleanup for high tensor count or approaching threshold
        console.log(`🧹 AGGRESSIVE CLEANUP: ${numTensors} tensors, ${(currentMemoryUsage / 1024 / 1024).toFixed(1)}MB used`);
        
        // Step 1: Run garbage collection
        tf.tidy(() => {}); // Force immediate tensor cleanup
        
        // Step 2: Dispose variables if still high
        const afterTidyInfo = tf.memory();
        if (afterTidyInfo.numTensors > 50) {
          await tf.disposeVariables();
          console.log('🧹 DISPOSED VARIABLES: Forced cleanup of persistent tensors');
        }
        
        // Step 3: Backend-specific optimizations
        if (tf.getBackend() === 'webgl') {
          try {
            // @ts-ignore - Access internal methods
            const webglBackend = tf.backend();
            if (webglBackend && typeof webglBackend.numDataIds === 'function') {
              const dataIdsBeforeDispose = webglBackend.numDataIds();
              
              // Force texture disposal in WebGL backend
              if (typeof webglBackend.disposeData === 'function') {
                // Find unused tensors and dispose them
                const unusedTensorIds = [];
                // This is a simplified approach - in production we'd need more sophisticated tracking
                tf.tidy(() => {
                  // Create and immediately dispose a tensor to trigger cleanup
                  const tempTensor = tf.zeros([1]);
                  tempTensor.dispose();
                });
              }
              
              // Log WebGL-specific cleanup
              const dataIdsAfterDispose = webglBackend.numDataIds ? webglBackend.numDataIds() : 0;
              console.log(`🧹 WEBGL CLEANUP: Released ${dataIdsBeforeDispose - dataIdsAfterDispose} WebGL textures`);
            }
          } catch (webglError) {
            console.warn('⚠️ WebGL cleanup error:', webglError);
          }
        }
        
        // Log memory cleanup results
        const newMemInfo = tf.memory();
        const freedMemory = currentMemoryUsage - newMemInfo.numBytes;
        const freedTensors = numTensors - newMemInfo.numTensors;
        
        console.log(`🧹 MEMORY CLEANUP RESULTS: 
          - Released: ${(freedMemory / 1024 / 1024).toFixed(1)}MB (${freedTensors} tensors)
          - Before: ${(currentMemoryUsage / 1024 / 1024).toFixed(1)}MB (${numTensors} tensors)
          - After: ${(newMemInfo.numBytes / 1024 / 1024).toFixed(1)}MB (${newMemInfo.numTensors} tensors)
          - Reduction: ${freedMemory > 0 ? ((freedMemory / currentMemoryUsage) * 100).toFixed(1) + '%' : '0%'}
        `);
        
        // Adjust cleanup interval based on memory pressure
        if (freedMemory > 50 * 1024 * 1024) { // If we freed more than 50MB
          // Memory pressure is high, clean up more frequently
          memoryCleanupInterval.current = 3000; // 3 seconds
        } else if (newMemInfo.numBytes < tensorMemoryThreshold.current * 0.5) {
          // Memory usage is low, can clean up less frequently
          memoryCleanupInterval.current = 8000; // 8 seconds
        } else {
          // Normal memory pressure
          memoryCleanupInterval.current = 5000; // 5 seconds
        }
      } else if (shouldLog(300)) {
        // Just log status if memory usage is acceptable
        console.log(`💾 MEMORY STATUS: ${(memInfo.numBytes / 1024 / 1024).toFixed(1)}MB used, ${memInfo.numTensors} tensors`);
      }
      
      lastMemoryCleanup.current = now;
      
    } catch (error) {
      if (shouldLog(600)) {
        console.warn('⚠️ Memory cleanup error:', error);
      }
    }
  };

  useEffect(() => {
    const initBlazePose = async () => {
      console.log('🔄 PHASE 4 ENHANCED: Initializing BlazePose with coordinate fix...');
      console.log('🎯 PHASE 4 ENHANCED: Model Quality:', modelQuality);
      
      try {
        await tf.ready();
        
        // Enhanced WebGL configuration for better performance and memory management
        try {
          // Try to use WebGL backend first
          await tf.setBackend('webgl');
          await tf.ready();
          
          // Configure WebGL for better memory management and performance
          if (tf.getBackend() === 'webgl') {
            // More conservative memory cleanup settings for stability
            tf.env().set('WEBGL_DELETE_TEXTURE_THRESHOLD', 10); // More conservative texture deletion
            tf.env().set('WEBGL_FLUSH_THRESHOLD', 5); // Less aggressive flushing
            tf.env().set('WEBGL_FORCE_F16_TEXTURES', false); // Use 32-bit for better precision
            tf.env().set('WEBGL_PACK', true); // Enable texture packing for better performance
            tf.env().set('WEBGL_PACK_DEPTHWISECONV', true); // Enable optimized depthwise convolutions
            tf.env().set('WEBGL_MAX_TEXTURE_SIZE', 4096); // Limit max texture size to prevent OOM
            
            // Dynamically adjust memory threshold based on device capabilities
            try {
              // Check if we can detect device memory
              if (navigator && 'deviceMemory' in navigator) {
                // Device memory in GB (using type assertion since TypeScript might not recognize deviceMemory)
                const deviceMemoryGB = (navigator as any).deviceMemory as number;
                console.log(`📊 DEVICE MEMORY: ${deviceMemoryGB}GB detected`);
                
                // Adjust memory threshold based on available device memory
                // Use a percentage of available memory (more conservative on low-memory devices)
                if (deviceMemoryGB <= 2) {
                  // Low memory device (<=2GB): Use 200MB max
                  tensorMemoryThreshold.current = 200 * 1024 * 1024;
                  memoryCleanupInterval.current = 8000; // Less frequent cleanup for stability
                  console.log('📉 LOW MEMORY DEVICE: Using conservative memory settings');
                } else if (deviceMemoryGB <= 4) {
                  // Medium memory device (<=4GB): Use 400MB max
                  tensorMemoryThreshold.current = 400 * 1024 * 1024;
                  memoryCleanupInterval.current = 10000; // Increased cleanup interval
                  console.log('📊 MEDIUM MEMORY DEVICE: Using balanced memory settings');
                } else {
                  // High memory device (>4GB): Use 600MB max
                  tensorMemoryThreshold.current = 600 * 1024 * 1024;
                  memoryCleanupInterval.current = 15000; // Much less frequent cleanup
                  console.log('📈 HIGH MEMORY DEVICE: Using performance-oriented memory settings');
                }
              } else {
                // Can't detect device memory, use default settings
                console.log('📊 DEVICE MEMORY: Unable to detect, using default memory settings');
              }
            } catch (memoryDetectionError) {
              console.warn('⚠️ Memory detection error:', memoryDetectionError);
            }
            
            // Set up automatic garbage collection - CRASH FIX: Clear existing interval first
            if (typeof window !== 'undefined') {
              // CRASH FIX: Clear any existing cleanup interval to prevent multiple intervals
              if ((window as any).__tfCleanupInterval) {
                console.log('🧹 CRASH FIX: Clearing existing cleanup interval');
                clearInterval((window as any).__tfCleanupInterval);
                delete (window as any).__tfCleanupInterval;
              }
              
              // Add periodic tensor cleanup with adaptive behavior
              const cleanup = () => {
                try {
                  const memInfo = tf.memory();
                  const numTensors = memInfo.numTensors;
                  const currentMemoryUsage = memInfo.numBytes;
                  const memoryPressure = currentMemoryUsage / tensorMemoryThreshold.current;
                  
                  // Only log cleanup info periodically to reduce console spam
                  const shouldLogCleanup = Date.now() % 30000 < 1000; // Once every 30 seconds
                  
                  // Adaptive cleanup based on memory pressure and tensor count
                  if (numTensors > 50 || memoryPressure > 0.5) {
                    // Basic cleanup with tf.tidy
                    tf.tidy(() => {});
                    
                    if (shouldLogCleanup) {
                      const afterTidyInfo = tf.memory();
                      const freedTensors = numTensors - afterTidyInfo.numTensors;
                      const freedMemory = currentMemoryUsage - afterTidyInfo.numBytes;
                      console.log(`🧹 AUTO-CLEANUP: Released ${freedTensors} tensors (${(freedMemory / 1024 / 1024).toFixed(1)}MB)`);
                    }
                  }
                } catch (e) {
                  console.warn('⚠️ Auto cleanup error:', e);
                }
              };
              
              // Adaptive cleanup interval based on device memory
              const cleanupIntervalTime = memoryCleanupInterval.current;
              console.log(`⏱️ AUTO-CLEANUP: Scheduled every ${cleanupIntervalTime/1000} seconds`);
              const cleanupInterval = setInterval(cleanup, cleanupIntervalTime);
              
              // Store interval ID for cleanup on unmount
              (window as any).__tfCleanupInterval = cleanupInterval;
            }
          }
          
          console.log('✅ WebGL backend ready for Phase 4 BlazePose (optimized with enhanced memory management)');
        } catch (webglError) {
          console.warn('⚠️ WebGL failed, falling back to CPU:', webglError);
          await tf.setBackend('cpu');
          await tf.ready();
          
          // Adjust memory settings for CPU backend (typically less memory available)
          tensorMemoryThreshold.current = 200 * 1024 * 1024; // 200MB for CPU
          memoryCleanupInterval.current = 3000; // More frequent cleanup
          console.log('📉 CPU BACKEND: Using conservative memory settings');
          console.log('✅ CPU backend ready for Phase 4 BlazePose');
        }

        console.log('📊 TensorFlow.js Backend Info:', {
          backend: tf.getBackend(),
          memory: tf.memory(),
          platform: tf.env().platform
        });
        
        // 🎯 ENHANCED MODEL SELECTION: More robust model selection with better logging
        const getOptimalModelType = (): 'lite' | 'full' | 'heavy' => {
          // Define back-facing scenarios more precisely
          const isBackFacing = videoSetup?.toLowerCase().includes('out') || 
                              videoSetup?.toLowerCase().includes('offset');
          
          // Define model type mapping with fallbacks
          const modelTypeMap: Record<string, 'lite' | 'full' | 'heavy'> = {
            'Lite': 'lite',
            'Full': 'full',
            'Heavy': 'heavy'
          };
          
          // Default to 'full' if modelQuality is invalid
          const requestedType = modelTypeMap[modelQuality] || 'full';
          
          // For Treadmill setup, always honor user's model selection
          if (videoSetup === 'Treadmill') {
            console.log('🔧 INTELLIGENT MODEL: Using BlazePose', modelQuality, 'for Treadmill setup (user preference honored)');
            
            // Log detailed model selection reasoning
            console.log('📊 MODEL SELECTION DETAILS:', {
              videoSetup: videoSetup,
              userSelectedModel: modelQuality,
              actualModelUsed: requestedType,
              reason: 'Treadmill setup - user preference honored',
              isBackFacing: false
            });
            
            return requestedType;
          }
          
          // For back-facing scenarios, use Lite model regardless of user selection
          if (isBackFacing) {
            console.log('🔧 INTELLIGENT MODEL: Using BlazePose Lite for back-facing scenario:', videoSetup);
            
            // Log detailed model selection reasoning
            console.log('📊 MODEL SELECTION DETAILS:', {
              videoSetup: videoSetup,
              userSelectedModel: modelQuality,
              actualModelUsed: 'lite',
              reason: 'Back-facing scenario requires Lite model for better detection',
              isBackFacing: true,
              originalSelection: requestedType
            });
            
            return 'lite';
          }
          
          // For all other front-facing scenarios, use requested model quality
          console.log('🔧 INTELLIGENT MODEL: Using BlazePose', modelQuality, 'for front-facing scenario:', videoSetup);
          
          // Log detailed model selection reasoning
          console.log('📊 MODEL SELECTION DETAILS:', {
            videoSetup: videoSetup,
            userSelectedModel: modelQuality,
            actualModelUsed: requestedType,
            reason: 'Front-facing scenario - using requested model quality',
            isBackFacing: false
          });
          
          return requestedType;
        };

        const selectedModelType = getOptimalModelType();

        // Enhanced BlazePose configuration for better performance and reliability
        const blazePoseConfig: poseDetection.BlazePoseTfjsModelConfig = {
          runtime: 'tfjs' as const,
          modelType: selectedModelType,
          enableSmoothing: false, // We handle our own smoothing with custom filters
          enableSegmentation: false, // Disable segmentation for better performance
          smoothSegmentation: false,
          detectorModelUrl: undefined, // Let the library use the default URL
          landmarkModelUrl: undefined, // Let the library use the default URL
          // Note: BlazePoseTfjsModelConfig only supports a limited set of properties
          // The following properties are not part of the official API:
          // - multiPoseMaxDimension
          // - enableFaceKeypoints
          // - selfieMode
          // - cameraFps
          // We'll use the default configuration
        };
        
        console.log('🔧 PHASE 4 ENHANCED: BlazePose config:', blazePoseConfig);
        
        console.log('🔧 PHASE 4 ENHANCED: Creating BlazePose detector with final config:', blazePoseConfig);
        
        const blazePoseDetector = await poseDetection.createDetector(
          poseDetection.SupportedModels.BlazePose, 
          blazePoseConfig
        );
        
        if (!blazePoseDetector) {
          throw new Error('BlazePose detector creation returned null');
        }
        
        // Validate detector properties
        console.log('🔍 DETECTOR VALIDATION:', {
          detectorExists: !!blazePoseDetector,
          detectorType: typeof blazePoseDetector,
          hasEstimateMethod: typeof blazePoseDetector.estimatePoses === 'function',
          hasDisposeMethod: typeof blazePoseDetector.dispose === 'function',
          modelConfig: blazePoseConfig
        });
        
        console.log('✅ PHASE 4 ENHANCED: BlazePose detector created with coordinate fixes');
        
        // Implement model warm-up to ensure the model is fully loaded
        const warmUpModel = async (detector: poseDetection.PoseDetector): Promise<boolean> => {
          try {
            console.log('🔥 WARM-UP: Starting BlazePose model warm-up...');
            
            // Create a small blank canvas for warm-up
            const canvas = document.createElement('canvas');
            canvas.width = 256;
            canvas.height = 256;
            const ctx = canvas.getContext('2d');
            if (!ctx) {
              console.warn('⚠️ WARM-UP: Failed to create canvas context');
              return false;
            }
            
            // Fill with gray color (simulating a person silhouette)
            ctx.fillStyle = '#888888';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw a simple human-like shape
            ctx.fillStyle = '#555555';
            // Head
            ctx.beginPath();
            ctx.arc(128, 70, 30, 0, Math.PI * 2);
            ctx.fill();
            // Body
            ctx.fillRect(108, 100, 40, 80);
            // Legs
            ctx.fillRect(108, 180, 15, 60);
            ctx.fillRect(133, 180, 15, 60);
            
            // Run inference on warm-up canvas
            console.log('🔥 WARM-UP: Running inference on warm-up image...');
            const warmupResult = await detector.estimatePoses(
              canvas, 
              { 
                flipHorizontal: false,
                maxPoses: 1
              }
            );
            
            console.log('✅ WARM-UP: BlazePose model warm-up complete', {
              detectedPoses: warmupResult.length,
              modelType: selectedModelType,
              canvasSize: `${canvas.width}x${canvas.height}`
            });
            
            return true;
          } catch (error) {
            console.error('❌ WARM-UP: BlazePose model warm-up failed:', error);
            return false;
          }
        };
        
        // Perform model warm-up
        console.log('🔄 WARM-UP: Preparing BlazePose model...');
        const warmUpSuccess = await warmUpModel(blazePoseDetector);
        
        if (warmUpSuccess) {
          setDetector(blazePoseDetector);
          setIsInitialized(true);
          console.log('✅ PHASE 4 ENHANCED: BlazePose initialization complete with warm-up');
          setDebugInfo(`Phase 4 Enhanced: BlazePose (${modelQuality}) - Enhanced Tensor Processing - ${tf.getBackend()} - Warm-up Complete`);
        } else {
          console.warn('⚠️ WARM-UP: Proceeding without successful warm-up');
          setDetector(blazePoseDetector);
          setIsInitialized(true);
          console.log('✅ PHASE 4 ENHANCED: BlazePose initialization complete (without warm-up)');
          setDebugInfo(`Phase 4 Enhanced: BlazePose (${modelQuality}) - Enhanced Tensor Processing - ${tf.getBackend()}`);
        }
        
        // Test enhanced tensor processing pipeline if enabled - CRASH FIX: Disabled by default
        if (enableEnhancedTensorProcessing && false) { // Disabled for stability
          console.log('🧪 PHASE 4 ENHANCED: Testing tensor processing pipeline...');
          TensorPipelineTest.runAllTests().then(testResult => {
            if (testResult) {
              console.log('✅ PHASE 4 ENHANCED: Tensor processing pipeline tests PASSED');
              setDebugInfo(prev => prev + ' - Pipeline Tests: PASSED');
            } else {
              console.warn('⚠️ PHASE 4 ENHANCED: Tensor processing pipeline tests FAILED');
              setDebugInfo(prev => prev + ' - Pipeline Tests: FAILED');
            }
          }).catch(testError => {
            console.error('❌ PHASE 4 ENHANCED: Pipeline test error:', testError);
            setDebugInfo(prev => prev + ' - Pipeline Tests: ERROR');
          });
        }
        
      } catch (error) {
        console.error('❌ PHASE 4 ENHANCED: BlazePose initialization failed:', error);
        setDebugInfo(`Phase 4 BlazePose Init Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        setIsInitialized(false);
        setDetector(null);
      }
    };

    initBlazePose();
    
    return () => {
      console.log('🧹 PHASE 4 ENHANCED: Starting comprehensive cleanup...');
      
      // 1. Clean up the detector
      if (detector) {
        console.log('🧹 PHASE 4 ENHANCED: Disposing BlazePose detector');
        try {
          detector.dispose();
        } catch (disposeError) {
          console.warn('⚠️ Error disposing BlazePose detector:', disposeError);
        }
      }
      
      // 2. Clean up TensorFlow.js resources
      try {
        console.log('🧹 PHASE 4 ENHANCED: Cleaning up TensorFlow.js resources');
        
        // Dispose any remaining tensors
        const initialTensorCount = tf.memory().numTensors;
        tf.disposeVariables();
        tf.tidy(() => {}); // Force garbage collection
        const finalTensorCount = tf.memory().numTensors;
        
        console.log(`🧹 TENSOR CLEANUP: Released ${initialTensorCount - finalTensorCount} tensors`);
      } catch (tfError) {
        console.warn('⚠️ Error cleaning up TensorFlow.js resources:', tfError);
      }
      
      // 3. Clear any intervals
      if (typeof window !== 'undefined' && (window as any).__tfCleanupInterval) {
        console.log('🧹 PHASE 4 ENHANCED: Clearing automatic cleanup interval');
        clearInterval((window as any).__tfCleanupInterval);
        delete (window as any).__tfCleanupInterval;
      }
      
      // 4. Reset all filters
      try {
        console.log('🧹 PHASE 4 ENHANCED: Resetting filters');
        keypointsFilter.reset();
        visibilityFilter.reset();
        stabilityFilter.reset();
      } catch (filterError) {
        console.warn('⚠️ Error resetting filters:', filterError);
      }
      
      console.log('✅ PHASE 4 ENHANCED: Cleanup complete');
    };
  }, [modelQuality]);

  // Enhanced detectPoses function with improved tensor handling and validation
  const detectPoses = async (video: HTMLVideoElement): Promise<poseDetection.Pose[]> => {
    const startTime = performance.now();
    frameCount.current++;
    
    // Validate detector and video
    if (!detector || !video || !isInitialized) {
      if (shouldLog(300)) {
        console.log('❌ PHASE 4 ENHANCED: Detector or video not ready');
      }
      return [];
    }

    // Validate video readiness with detailed logging
    if (video.readyState < 2 || video.videoWidth === 0 || video.videoHeight === 0) {
      if (shouldLog(300)) {
        console.log('⏳ PHASE 4 ENHANCED: Video not ready for processing', {
          readyState: video.readyState,
          width: video.videoWidth,
          height: video.videoHeight,
          currentTime: video.currentTime
        });
      }
      return [];
    }
    
    // Prevent concurrent processing
    if (isProcessing.current) {
      if (shouldLog(300)) {
        console.log('⏭️ PHASE 4 ENHANCED: Skipping frame - previous frame still processing');
      }
      return lastPoseResult.current;
    }
    
    isProcessing.current = true;
    
    try {
      // PHASE 3: Memory management check
      await checkAndCleanupMemory();
      
      // PHASE 3: Implement smart frame skipping
      if (shouldSkipFrame()) {
        // Return last known good result for skipped frames
        if (shouldLog(180)) {
          console.log(`⏭️ PERFORMANCE: Skipping frame ${frameCount.current} (mode: ${performanceMode.current})`);
        }
        isProcessing.current = false;
        return lastPoseResult.current;
      }

      if (shouldLog(120)) {
        console.log('🚀 PHASE 4 ENHANCED: BlazePose with COORDINATE FIXES...');
      }
      
      // Get video dimensions with validation
      const imageSize = getImageSize(video);
      if (!imageSize || imageSize.width === 0 || imageSize.height === 0) {
        console.warn('⚠️ PHASE 4 ENHANCED: Invalid video dimensions:', imageSize);
        isProcessing.current = false;
        return [];
      }
      
      if (shouldLog(300)) {
        console.log('📐 PHASE 4 ENHANCED: Video dimensions:', imageSize);
      }
      
      // SECTION 1: RAW TENSOR PROCESSING PIPELINE - Replace estimatePoses() approach
      let poses: poseDetection.Pose[];

      // SECTION 3: Start performance monitoring
      const startTime = performance.now();

      try {
        console.log('🔧 SECTION 1: Starting raw tensor processing pipeline');

        // STEP 1: Use estimatePoses but intercept and process raw tensors
        // This is a transitional approach that maintains compatibility while adding raw tensor processing
        const estimationConfig: poseDetection.BlazePoseTfjsEstimationConfig = {
          maxPoses: 1,
          flipHorizontal: false,
        };

        // Get initial poses from detector (this gives us the basic structure)
        const initialPoses = await detector.estimatePoses(video, estimationConfig);
        console.log('🔧 SECTION 1: Initial poses from detector:', initialPoses.length);

        if (initialPoses.length === 0) {
          console.log('🔧 SECTION 1: No initial poses detected');
          poses = [];
        } else {
          const initialPose = initialPoses[0];
          console.log('🔧 SECTION 1: Processing initial pose with raw tensor pipeline');

          // STEP 2: SECTION 2 - Apply enhanced image processing
          console.log('🔧 SECTION 2: Applying enhanced image preprocessing');

          // Enhanced image processing configuration
          const imageConfig: Partial<ImageProcessingConfig> = {
            preserveAspectRatio: true,
            paddingColor: [0, 0, 0],
            interpolationMethod: 'bilinear',
            centerCrop: false,
            normalizationRange: [0, 1],
            qualityValidation: true
          };

          // Use enhanced image processing with configuration
          const targetSize = BLAZEPOSE_IMAGE_PROCESSING_CONFIG.landmarkInputSize;
          const imageTensor3D = convertImageToTensor(video, targetSize, true, imageConfig);
          const imageTensor = tf.expandDims(imageTensor3D, 0) as tf.Tensor4D;
          imageTensor3D.dispose(); // Clean up intermediate tensor

          console.log('🔧 SECTION 2: Created enhanced image tensor:', imageTensor.shape);

          // STEP 3: Process through tensor pipeline for coordinate refinement
          let refinedKeypoints = [...initialPose.keypoints];
          let refinedWorldKeypoints = initialPose.keypoints3D ? [...initialPose.keypoints3D] : [];

          try {
            // Apply tensor-based landmark processing
            const tensorConfig: TensorsToLandmarksConfig = {
              numLandmarks: 33,
              inputImageWidth: imageSize.width,
              inputImageHeight: imageSize.height,
              normalizeZ: 1.0,
              visibilityActivation: 'sigmoid',
              flipHorizontally: false,
              flipVertically: false
            };

            // Convert keypoints to tensor for processing
            const keypointsTensor = keypointsToTensor2D(refinedKeypoints);
            console.log('🔧 SECTION 1: Created keypoints tensor:', keypointsTensor.shape);

            // Apply tensor-based filtering and refinement
            const filteredTensor = filterNaNValues2D(keypointsTensor);
            const processedLandmarks = await tensorsToLandmarks(filteredTensor, tensorConfig);
            console.log('🔧 SECTION 1: Processed landmarks:', processedLandmarks.length);

            // STEP 4: Apply coordinate transformations
            if (processedLandmarks.length > 0) {
              // Create a simple ROI based on pose bounds
              const roi: Rect = {
                xCenter: 0.5,
                yCenter: 0.5,
                width: 0.8,
                height: 0.8
              };

              // Apply coordinate transformations
              const projectedLandmarks = calculateLandmarkProjection(processedLandmarks, roi);
              const finalKeypoints = normalizedKeypointsToKeypoints(projectedLandmarks, imageSize);

              // Update refined keypoints with names preserved
              refinedKeypoints = finalKeypoints.map((kp, index) => ({
                ...kp,
                name: initialPose.keypoints[index]?.name || `landmark_${index}`
              }));

              console.log('🔧 SECTION 1: Applied coordinate transformations');
            }

            // STEP 5: SECTION 3 - Enhanced 3D world coordinate processing
            if (initialPose.keypoints3D && initialPose.keypoints3D.length > 0) {
              console.log('🔧 SECTION 3: Processing 3D world coordinates with enhanced tensor pipeline');

              try {
                // Create world tensor from keypoints3D data
                const worldTensor = keypointsToTensor2D(initialPose.keypoints3D);
                console.log('🔧 SECTION 3: Created world tensor:', worldTensor.shape);

                // Use enhanced world landmark processing
                const processedWorldLandmarks = await processWorldLandmarks(
                  worldTensor,
                  BLAZEPOSE_WORLD_LANDMARK_CONFIG_ENHANCED
                );

                if (processedWorldLandmarks.length > 0) {
                  const roi: Rect = {
                    xCenter: 0.5,
                    yCenter: 0.5,
                    width: 0.8,
                    height: 0.8
                  };

                  // Apply enhanced world landmark projection
                  refinedWorldKeypoints = calculateWorldLandmarkProjection(processedWorldLandmarks, roi);
                  console.log('🔧 SECTION 3: Enhanced world landmarks processed:', refinedWorldKeypoints.length);

                  // Validate 3D coordinates
                  const validWorldKeypoints = refinedWorldKeypoints.filter(kp =>
                    !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.z || 0)
                  );
                  console.log('🔧 SECTION 3: Valid 3D coordinates:', validWorldKeypoints.length, '/', refinedWorldKeypoints.length);
                } else {
                  console.warn('🔧 SECTION 3: No valid world landmarks processed');
                }

                // Clean up world tensor
                worldTensor.dispose();

              } catch (worldProcessingError) {
                console.error('❌ SECTION 3: World coordinate processing failed:', worldProcessingError);
                // Fallback to original coordinates with basic validation
                refinedWorldKeypoints = initialPose.keypoints3D.map((kp, index) => ({
                  x: isNaN(kp.x) ? 0 : kp.x,
                  y: isNaN(kp.y) ? 0 : kp.y,
                  z: isNaN(kp.z || 0) ? 0 : (kp.z || 0),
                  score: kp.score || 0,
                  name: kp.name || `world_landmark_${index}`
                }));
              }
            }

            // Clean up tensors
            keypointsTensor.dispose();
            filteredTensor.dispose();

          } catch (tensorError) {
            console.warn('⚠️ SECTION 1: Tensor processing failed, using original coordinates:', tensorError);
            // Keep original coordinates if tensor processing fails
          }

          // STEP 6: Create final pose with refined coordinates
          const refinedPose: poseDetection.Pose = {
            keypoints: refinedKeypoints,
            keypoints3D: refinedWorldKeypoints,
            score: initialPose.score || 0.8
          };

          poses = [refinedPose];
          console.log('🔧 SECTION 1: Raw tensor processing complete - refined pose with',
            refinedKeypoints.length, '2D keypoints and', refinedWorldKeypoints.length, '3D keypoints');

          // Clean up main tensor
          imageTensor.dispose();
        }

        // Force garbage collection
        tf.tidy(() => {});

        // SECTION 3: Update performance metrics
        const processingTime = performance.now() - startTime;
        updateEnhancedPerformanceMetrics(processingTime);

        // SECTION 3: Perform memory cleanup if needed
        performMemoryCleanup();

      } catch (estimationError) {
        console.error('❌ SECTION 1: Raw tensor processing failed:', estimationError);
        console.log('📊 VIDEO INFO:', {
          element: 'HTMLVideoElement',
          width: video.videoWidth,
          height: video.videoHeight,
          readyState: video.readyState,
          currentTime: video.currentTime
        });
        // Clean up tensors even on error
        tf.tidy(() => {});
        throw estimationError; // Re-throw to be caught by outer try-catch
      }
      
      // Ensure poses is an array and validate structure
      const posesArray = Array.isArray(poses) ? poses : [];
      
      // Enhanced validation of raw poses from BlazePose
      if (posesArray.length > 0) {
        const pose = posesArray[0];
        const nanKeypoints2D = pose.keypoints?.filter(kp => isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score)) || [];
        const nanKeypoints3D = pose.keypoints3D?.filter(kp => isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.z)) || [];
        
        if (nanKeypoints2D.length > 0 || nanKeypoints3D.length > 0) {
          console.warn('⚠️ RAW BLAZEPOSE NaN DETECTION:', {
            nanKeypoints2D: nanKeypoints2D.length,
            nanKeypoints3D: nanKeypoints3D.length,
            totalKeypoints2D: pose.keypoints?.length || 0,
            totalKeypoints3D: pose.keypoints3D?.length || 0,
            nanRatio2D: pose.keypoints ? (nanKeypoints2D.length / pose.keypoints.length * 100).toFixed(1) + '%' : '0%',
            nanRatio3D: pose.keypoints3D ? (nanKeypoints3D.length / pose.keypoints3D.length * 100).toFixed(1) + '%' : '0%'
          });
          
          // Use comprehensive debugging for NaN issues
          if (frameCount.current % 30 === 0) { // Debug every 30 frames when NaN detected
            debugBlazePoseStructure(pose, frameCount.current);
            debugTensorMemory();
          }
        }
        
        // Quick health check
        const healthCheck = blazePoseHealthCheck(pose);
        if (!healthCheck.isHealthy && shouldLog(180)) {
          console.warn(`🏥 POSE HEALTH CHECK (${healthCheck.severity}):`, healthCheck.issues);
        }
      }
      
      if (shouldLog(120)) {
        console.log(`✅ PHASE 4 FIXED: BlazePose detected ${posesArray.length} poses (using original coordinates)`);
      }
      
      if (posesArray.length > 0) {
        const pose = posesArray[0];
        
        // Validate pose structure before processing
        if (!pose || !pose.keypoints || pose.keypoints.length === 0) {
          console.warn('⚠️ PHASE 4 ENHANCED: Invalid pose structure:', pose);
          isProcessing.current = false;
          return [];
        }
        
        // 🎯 POSE VALIDATION: Enhanced validation with detailed logging
        const poseQuality = validatePoseQuality(pose, videoSetup);
        if (!poseQuality.isValid) {
          console.log('⚠️ POSE VALIDATION: Low quality pose detected:', {
            reason: poseQuality.reason,
            confidence: poseQuality.confidence,
            videoSetup: videoSetup,
            keypointCount: pose.keypoints.length,
            keypoint3DCount: pose.keypoints3D?.length || 0
          });
          // Continue processing - let coordinate repair handle NaN values
        }
        
        // PHASE 4: Apply processing - CRASH FIX: Use lightweight processing by default
        const processedPose = enableEnhancedTensorProcessing 
          ? await processBlazePoseWithEnhancedTensorPipeline(pose, imageSize)
          : processBlazePoseWithBasicFixes(pose, imageSize);
        
        // Validate processed pose before returning
        if (!processedPose || !processedPose.keypoints || processedPose.keypoints.length === 0) {
          console.warn('⚠️ PHASE 4 ENHANCED: Invalid processed pose structure');
          isProcessing.current = false;
          return [];
        }
        
        // Periodic detailed logging
        if (frameCount.current % 30 === 0) {
          console.log('🎯 PHASE 4 ENHANCED: COORDINATE-FIXED POSE AUDIT (periodic):');
          
          if (processedPose.keypoints && processedPose.keypoints.length > 0) {
            const sampleKeypoint = processedPose.keypoints[0];
            console.log('✅ COORDINATE-FIXED 2D KEYPOINTS:', {
              count: processedPose.keypoints.length,
              sample: {
                x: sampleKeypoint.x?.toFixed(2),
                y: sampleKeypoint.y?.toFixed(2),
                score: sampleKeypoint.score?.toFixed(3),
                isValid: !isNaN(sampleKeypoint.x) && !isNaN(sampleKeypoint.y)
              }
            });
            
            // Count valid keypoints
            const validKeypoints = processedPose.keypoints.filter(
              kp => !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.score)
            );
            console.log(`📊 KEYPOINT QUALITY: ${validKeypoints.length}/${processedPose.keypoints.length} valid keypoints`);
          }
          
          if (processedPose.keypoints3D && processedPose.keypoints3D.length > 0) {
            const sample3D = processedPose.keypoints3D[0];
            console.log('✅ COORDINATE-FIXED 3D WORLD LANDMARKS:', {
              count: processedPose.keypoints3D.length,
              sample: {
                x: sample3D.x?.toFixed(4),
                y: sample3D.y?.toFixed(4),
                z: sample3D.z?.toFixed(4),
                isValid: !isNaN(sample3D.x) && !isNaN(sample3D.y) && !isNaN(sample3D.z)
              }
            });
            
            // Count valid 3D keypoints
            const valid3DKeypoints = processedPose.keypoints3D.filter(
              kp => !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.z)
            );
            console.log(`📊 3D KEYPOINT QUALITY: ${valid3DKeypoints.length}/${processedPose.keypoints3D.length} valid 3D keypoints`);
          }
        }
        
        // PHASE 3: Track performance and store result
        const processingTime = performance.now() - startTime;
        updatePerformanceMetrics(processingTime);
        lastPoseResult.current = [processedPose];
        
        isProcessing.current = false;
        return [processedPose];
      } else {
        // PHASE 3: Track performance even for empty results
        const processingTime = performance.now() - startTime;
        updatePerformanceMetrics(processingTime);
        lastPoseResult.current = [];
        
        if (shouldLog(180)) {
          console.log('⚠️ PHASE 4 ENHANCED: No poses detected');
        }
        
        isProcessing.current = false;
        return [];
      }
      
    } catch (error) {
      // PHASE 3: Track performance even for errors
      const processingTime = performance.now() - startTime;
      updatePerformanceMetrics(processingTime);
      
      // Enhanced error logging with classification
      const errorType = error instanceof Error && error.message.includes('tensor') 
        ? 'TENSOR ERROR' 
        : error instanceof Error && error.message.includes('memory')
          ? 'MEMORY ERROR'
          : 'DETECTION ERROR';
      
      console.error(`❌ PHASE 4 ENHANCED: BlazePose ${errorType}:`, error);
      setDebugInfo(`Phase 4 BlazePose ${errorType}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      // Track errors and implement CPU fallback for stability
      const currentTime = Date.now();
      if (currentTime - lastErrorTime.current > 30000) { // Reset error count every 30 seconds
        errorCount.current = 0;
      }
      errorCount.current++;
      lastErrorTime.current = currentTime;
      
      // If we have too many errors and WebGL is active, try CPU fallback
      if (errorCount.current >= 3 && tf.getBackend() === 'webgl' && !cpuFallbackActive.current) {
        console.warn(`⚠️ TOO MANY ERRORS (${errorCount.current}): Attempting CPU fallback for stability`);
        setTimeout(() => forceUseCPU(), 1000); // Async fallback to avoid blocking
      }
      
      isProcessing.current = false;
      return [];
    }
  };

  // 🎯 ENHANCED POSE QUALITY VALIDATION: Improved handling of NaN values and coordinate validation
  const validatePoseQuality = (pose: poseDetection.Pose, videoSetup?: string): { 
    isValid: boolean; 
    reason: string; 
    confidence: number;
    nanCount?: number;
    nanKeypoints?: string[];
    visibilityStats?: {
      face: number;
      body: number;
      total: number;
    }
  } => {
    // Validate pose structure
    if (!pose || !pose.keypoints || pose.keypoints.length === 0) {
      return {
        isValid: false,
        reason: 'Invalid pose structure - missing keypoints',
        confidence: 0.0
      };
    }
    
    // Enhanced NaN detection with detailed reporting
    const nanKeypoints: string[] = [];
    let nanCount = 0;
    
    // Check for NaN values in 2D keypoints with detailed tracking
    pose.keypoints.forEach((kp, index) => {
      if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score)) {
        nanCount++;
        nanKeypoints.push(kp.name || `keypoint_${index}`);
      }
    });
    
    // Check for NaN values in 3D keypoints
    let nan3DCount = 0;
    if (pose.keypoints3D && pose.keypoints3D.length > 0) {
      pose.keypoints3D.forEach((kp, index) => {
        if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.z)) {
          nan3DCount++;
          // Only add to nanKeypoints if not already included from 2D check
          const keypointName = kp.name || `keypoint3D_${index}`;
          if (!nanKeypoints.includes(keypointName)) {
            nanKeypoints.push(keypointName);
          }
        }
      });
    }
    
    // Determine if this is a back-facing scenario
    const isBackFacing = videoSetup?.toLowerCase().includes('out') || 
                         videoSetup?.toLowerCase().includes('offset');
    
    // For back-facing scenarios, we're more tolerant of NaN values in face keypoints
    const isTreadmill = videoSetup === 'Treadmill';
    
    // Different thresholds based on video setup
    const nanThreshold = isBackFacing ? 0.5 : isTreadmill ? 0.2 : 0.3;
    const nanRatio = nanCount / pose.keypoints.length;
    
    if (nanRatio > nanThreshold) {
      return {
        isValid: false,
        reason: `${nanCount} NaN coordinates detected (${(nanRatio * 100).toFixed(1)}%) - ${isBackFacing ? 'back-facing' : 'front-facing'} pose`,
        confidence: 1.0 - nanRatio,
        nanCount,
        nanKeypoints,
        visibilityStats: { face: 0, body: 0, total: 0 }
      };
    }
    
    // Enhanced visibility checking with separate face and body analysis
    
    // Check face keypoint visibility (indices 0-10 are facial landmarks)
    const faceKeypoints = pose.keypoints.slice(0, 11);
    const visibleFaceKeypoints = faceKeypoints.filter(kp => kp.score > 0.3 && !isNaN(kp.score));
    const faceVisibilityRatio = faceKeypoints.length > 0 ? visibleFaceKeypoints.length / faceKeypoints.length : 0;
    
    // Check body keypoint visibility (indices 11-32 are body landmarks)
    const bodyKeypoints = pose.keypoints.slice(11);
    const visibleBodyKeypoints = bodyKeypoints.filter(kp => kp.score > 0.2 && !isNaN(kp.score));
    const bodyVisibilityRatio = bodyKeypoints.length > 0 ? visibleBodyKeypoints.length / bodyKeypoints.length : 0;
    
    // Calculate total visibility
    const totalVisibleKeypoints = visibleFaceKeypoints.length + visibleBodyKeypoints.length;
    const totalVisibilityRatio = pose.keypoints.length > 0 ? totalVisibleKeypoints / pose.keypoints.length : 0;
    
    // Store visibility stats for reporting
    const visibilityStats = {
      face: faceVisibilityRatio,
      body: bodyVisibilityRatio,
      total: totalVisibilityRatio
    };
    
    // For back-facing scenarios, we care more about body visibility than face visibility
    if (isBackFacing) {
      // For back-facing, body is more important than face
      if (bodyVisibilityRatio < 0.3) {
        return {
          isValid: false,
          reason: 'Low body visibility in back-facing pose',
          confidence: bodyVisibilityRatio,
          nanCount,
          nanKeypoints,
          visibilityStats
        };
      }
    } else {
      // For front-facing (especially Treadmill), we expect good face visibility
      if (faceVisibilityRatio < 0.3 && isTreadmill) {
        return {
          isValid: false,
          reason: 'Low face visibility in Treadmill setup - person may be facing away',
          confidence: faceVisibilityRatio,
          nanCount,
          nanKeypoints,
          visibilityStats
        };
      }
      
      // For all front-facing scenarios, we need decent body visibility
      if (bodyVisibilityRatio < 0.4) {
        return {
          isValid: false,
          reason: 'Low body visibility detected - insufficient pose data',
          confidence: bodyVisibilityRatio,
          nanCount,
          nanKeypoints,
          visibilityStats
        };
      }
    }
    
    // Calculate overall confidence score
    // For back-facing, weight body more heavily; for front-facing, weight face and body equally
    const confidenceScore = isBackFacing 
      ? (bodyVisibilityRatio * 0.8) + (faceVisibilityRatio * 0.2)
      : (bodyVisibilityRatio * 0.5) + (faceVisibilityRatio * 0.5);
    
    return {
      isValid: true,
      reason: `Valid ${isBackFacing ? 'back-facing' : 'front-facing'} pose detected`,
      confidence: confidenceScore,
      nanCount,
      nanKeypoints,
      visibilityStats
    };
  };

  // PHASE 4 ENHANCED: Complete tensor processing pipeline
  const processBlazePoseWithEnhancedTensorPipeline = async (pose: poseDetection.Pose, imageSize: { width: number; height: number }): Promise<poseDetection.Pose> => {
    console.log('🔧 PHASE 4 ENHANCED: Processing pose with complete tensor pipeline...');
    
    try {
      // Validate input pose and image size
      if (!pose || !pose.keypoints || pose.keypoints.length === 0) {
        console.warn('⚠️ ENHANCED TENSOR PIPELINE: Invalid pose structure');
        return pose;
      }
      
      if (!imageSize || imageSize.width <= 0 || imageSize.height <= 0) {
        console.warn('⚠️ ENHANCED TENSOR PIPELINE: Invalid image size:', imageSize);
        return pose;
      }
      
      let processedKeypoints = [...pose.keypoints];
      let processedKeypoints3D = pose.keypoints3D ? [...pose.keypoints3D] : [];
      
      console.log('🔧 ENHANCED TENSOR PIPELINE: Starting tensor-based coordinate processing');
      
      // STEP 1: Apply comprehensive coordinate validation and cleaning
      console.log('🔧 ENHANCED TENSOR PIPELINE: STEP 1 - Coordinate validation and cleaning');
      const cleanedResults = validateAndCleanPoseCoordinates(processedKeypoints, processedKeypoints3D);
      processedKeypoints = cleanedResults.landmarks;
      processedKeypoints3D = cleanedResults.worldLandmarks;
      
      // STEP 2: Apply tensor-based landmark refinement (if we have sufficient data)
      if (processedKeypoints.length >= 33) { // BlazePose has 33 landmarks
        console.log('🔧 ENHANCED TENSOR PIPELINE: STEP 2 - Applying tensor-based landmark refinement');
        
        try {
          // Apply custom tensors to landmarks processing
          const tensorConfig: TensorsToLandmarksConfig = {
            numLandmarks: 33,
            inputImageWidth: imageSize.width,
            inputImageHeight: imageSize.height,
            normalizeZ: 1.0,
            visibilityActivation: 'sigmoid',
            flipHorizontally: false,
            flipVertically: false
          };
          
          // Convert keypoints to tensor using proper type-safe method
          const keypointsTensor = keypointsToTensor2D(processedKeypoints);
          
          // Apply tensor-based filtering for NaN values with proper 2D output
          const filteredTensor = filterNaNValues2D(keypointsTensor);
          
          // Process through tensors to landmarks pipeline
          const refinedLandmarks = await tensorsToLandmarks(filteredTensor, tensorConfig);
          
          // Add landmark names back
          const namedRefinedLandmarks = refinedLandmarks.map((landmark, index) => ({
            ...landmark,
            name: processedKeypoints[index]?.name || `landmark_${index}`
          }));
          
          processedKeypoints = namedRefinedLandmarks.length > 0 ? namedRefinedLandmarks : processedKeypoints;
          
          // Clean up tensors properly
          safeTensorDispose([keypointsTensor, filteredTensor]);
          
          console.log('✅ ENHANCED TENSOR PIPELINE: STEP 2 complete - Tensor-based refinement applied');
          
        } catch (tensorError) {
          console.warn('⚠️ ENHANCED TENSOR PIPELINE: STEP 2 failed, continuing with cleaned coordinates:', tensorError);
        }
      }
      
      // STEP 3: Apply advanced coordinate repair with enhanced strategies
      console.log('🔧 ENHANCED TENSOR PIPELINE: STEP 3 - Advanced coordinate repair');
      processedKeypoints = await applyEnhancedCoordinateRepair(processedKeypoints, imageSize);
      processedKeypoints3D = await applyEnhanced3DCoordinateRepair(processedKeypoints3D);
      
      // STEP 4: Apply smoothing and stability filtering
      console.log('🔧 ENHANCED TENSOR PIPELINE: STEP 4 - Smoothing and stability filtering');
      if (processedKeypoints && processedKeypoints.length > 0) {
        processedKeypoints = keypointsFilter.apply(processedKeypoints);
        
        const stabilityResult = stabilityFilter.apply(processedKeypoints);
        processedKeypoints = stabilityResult.keypoints;
        
        if (frameCount.current % 60 === 0) {
          console.log('✅ ENHANCED TENSOR PIPELINE: Applied enhanced stability filter:', {
            isStable: stabilityResult.isStable,
            confidence: stabilityResult.confidence.toFixed(3)
          });
        }
      }
      
      // Apply visibility smoothing to 3D landmarks
      if (processedKeypoints3D && processedKeypoints3D.length > 0) {
        processedKeypoints3D = visibilityFilter.apply(processedKeypoints3D);
      }
      
      // Create the enhanced processed pose
      const enhancedPose: poseDetection.Pose = {
        ...pose,
        keypoints: processedKeypoints,
        keypoints3D: processedKeypoints3D
      };
      
      if (frameCount.current % 60 === 0) {
        console.log('✅ ENHANCED TENSOR PIPELINE: Complete processing finished');
        console.log('✅ ENHANCED TENSOR PIPELINE: Final pose quality:', {
          keypoints2D: processedKeypoints.length,
          keypoints3D: processedKeypoints3D.length,
          validKeypoints2D: processedKeypoints.filter(kp => !isNaN(kp.x) && !isNaN(kp.y)).length,
          validKeypoints3D: processedKeypoints3D.filter(kp => !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.z)).length
        });
      }
      
      return enhancedPose;
      
    } catch (enhancedError) {
      console.error('❌ ENHANCED TENSOR PIPELINE: Processing error:', enhancedError);
      
      // Fallback to standard coordinate fixes
      console.log('🔄 ENHANCED TENSOR PIPELINE: Falling back to standard coordinate fixes');
      return await processBlazePoseWithCoordinateFixes(pose, imageSize);
    }
  };

  // Enhanced coordinate repair functions
  const applyEnhancedCoordinateRepair = async (keypoints: any[], imageSize: { width: number; height: number }): Promise<any[]> => {
    console.log('🔧 ENHANCED COORDINATE REPAIR: Applying advanced repair strategies');
    
    // Apply the same repair logic as before, but with enhanced logging
    return keypoints.map((kp, index) => {
      if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score)) {
        // Use enhanced repair strategies
        const symmetricIndex = getSymmetricKeypointIndex(index);
        if (symmetricIndex !== -1) {
          const symmetricKeypoint = keypoints[symmetricIndex];
          if (symmetricKeypoint && !isNaN(symmetricKeypoint.x) && !isNaN(symmetricKeypoint.y)) {
            const centerX = imageSize.width / 2;
            const mirroredX = 2 * centerX - symmetricKeypoint.x;
            
            return {
              ...kp,
              x: mirroredX,
              y: symmetricKeypoint.y,
              score: symmetricKeypoint.score * 0.8,
              name: kp.name
            };
          }
        }
        
        // Fallback to anatomical position
        const anatomicalPosition = getAnatomicalPosition(index, imageSize);
        if (anatomicalPosition) {
          return {
            ...kp,
            x: anatomicalPosition.x,
            y: anatomicalPosition.y,
            score: anatomicalPosition.score,
            name: kp.name
          };
        }
        
        // Ultimate fallback
        return {
          ...kp,
          x: imageSize.width / 2,
          y: imageSize.height / 2,
          score: 0.1,
          name: kp.name
        };
      }
      
      return kp;
    });
  };

  const applyEnhanced3DCoordinateRepair = async (keypoints3D: any[]): Promise<any[]> => {
    if (!keypoints3D || keypoints3D.length === 0) return [];
    
    console.log('🔧 ENHANCED 3D COORDINATE REPAIR: Applying advanced 3D repair strategies');
    
    return keypoints3D.map((kp, index) => {
      if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.z)) {
        const anatomical3D = getAnatomical3DPosition(index);
        return {
          ...kp,
          x: anatomical3D.x,
          y: anatomical3D.y,
          z: anatomical3D.z,
          score: 0.1,
          visibility: 0.1
        };
      }
      return kp;
    });
  };

  // PHASE 4: Enhanced processing - Improved coordinate handling with robust NaN reconstruction
  const processBlazePoseWithCoordinateFixes = async (pose: poseDetection.Pose, imageSize: { width: number; height: number }): Promise<poseDetection.Pose> => {
    console.log('🔧 PHASE 4 FIXED: BlazePose coordinates with NaN detection and repair...');
    
    try {
      // We can't use tf.tidy with async functions directly, so we'll handle cleanup manually
      // Process the pose data
      // Validate input pose and image size
      if (!pose || !pose.keypoints || pose.keypoints.length === 0) {
        console.warn('⚠️ COORDINATE FIX: Invalid pose structure');
        return pose;
      }
      
      if (!imageSize || imageSize.width <= 0 || imageSize.height <= 0) {
        console.warn('⚠️ COORDINATE FIX: Invalid image size:', imageSize);
        return pose;
      }
      
      let processedKeypoints = [...pose.keypoints]; // Create a copy to avoid mutating the original
      let processedKeypoints3D = pose.keypoints3D ? [...pose.keypoints3D] : []; // Create a copy if it exists
      
      // 🛠️ ENHANCED NaN COORDINATE REPAIR: More robust repair with interpolation and symmetry
      const repairNaNCoordinates = (keypoints: any[]): any[] => {
          const currentTime = Date.now();
          
          // Reset repair attempts every 5 seconds to prevent permanent blocking
          if (currentTime - lastRepairTime.current > 5000) {
            repairAttempts.current = 0;
            lastRepairTime.current = currentTime;
          }
          
          // Check repair attempt limit
          if (repairAttempts.current >= MAX_REPAIR_ATTEMPTS) {
            console.log(`🚫 REPAIR LIMIT: Max attempts (${MAX_REPAIR_ATTEMPTS}) reached, using keypoints as-is`);
            return keypoints; // Return without repair to prevent infinite loops
          }
          
          // Count NaN keypoints and identify which ones need repair
          const validKeypoints = keypoints.filter(kp => !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.score));
          const nanKeypoints = keypoints.filter(kp => isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score));
          
          if (nanKeypoints.length === 0) {
            // No repair needed
            if (frameCount.current % 60 === 0) {
              console.log(`✅ NaN REPAIR: No repair needed - all ${keypoints.length} keypoints are valid`);
            }
            return keypoints;
          }
          
          // Increment repair attempt counter
          repairAttempts.current++;
          console.log(`🛠️ NaN REPAIR (${repairAttempts.current}/${MAX_REPAIR_ATTEMPTS}): Fixing ${nanKeypoints.length} NaN keypoints`);
          
          // Log height scaling info once per cycle
          if (repairAttempts.current === 1) {
            console.log(`📏 HEIGHT-BASED SCALING: User ${userHeight ? `${userHeight.feet}'${userHeight.inches}"` : '5\'9"'} (${userHeightMeters.toFixed(2)}m) - Scale Factor: ${heightScaleFactor.toFixed(2)}`);
          }
          
          // Determine if this is a back-facing scenario
          const isBackFacing = videoSetup?.toLowerCase().includes('out') || 
                              videoSetup?.toLowerCase().includes('offset');
          
          // Enhanced repair strategies
          return keypoints.map((kp, index) => {
            if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score)) {
              // STRATEGY 1: Try to use bilateral symmetry for repair
              const symmetricKeypointIndex = getSymmetricKeypointIndex(index);
              if (symmetricKeypointIndex !== -1) {
                const symmetricKeypoint = keypoints[symmetricKeypointIndex];
                if (symmetricKeypoint && !isNaN(symmetricKeypoint.x) && !isNaN(symmetricKeypoint.y)) {
                  // Use symmetric keypoint with mirroring around center
                  const centerX = imageSize.width / 2;
                  const mirroredX = 2 * centerX - symmetricKeypoint.x;
                  
                  if (index < 3 || frameCount.current % 60 === 0) {
                    console.log(`🛠️ SYMMETRY REPAIR: Using mirrored position for keypoint ${index} (${kp.name}) from keypoint ${symmetricKeypointIndex} (${symmetricKeypoint.name})`);
                  }
                  
                  return {
                    ...kp,
                    x: mirroredX,
                    y: symmetricKeypoint.y,
                    score: symmetricKeypoint.score * 0.8, // Slightly lower confidence for mirrored points
                    name: kp.name
                  };
                }
              }
              
              // STRATEGY 2: Try to use adjacent keypoints for interpolation
              const adjacentIndices = getAdjacentKeypointIndices(index);
              const validAdjacents = adjacentIndices
                .map(idx => keypoints[idx])
                .filter(adjKp => adjKp && !isNaN(adjKp.x) && !isNaN(adjKp.y));
              
              if (validAdjacents.length > 0) {
                // Calculate average position from adjacent keypoints
                const avgX = validAdjacents.reduce((sum, adjKp) => sum + adjKp.x, 0) / validAdjacents.length;
                const avgY = validAdjacents.reduce((sum, adjKp) => sum + adjKp.y, 0) / validAdjacents.length;
                const avgScore = validAdjacents.reduce((sum, adjKp) => sum + adjKp.score, 0) / validAdjacents.length * 0.7;
                
                if (index < 3 || frameCount.current % 60 === 0) {
                  console.log(`🛠️ INTERPOLATION REPAIR: Using adjacent keypoints for ${index} (${kp.name})`);
                }
                
                return {
                  ...kp,
                  x: avgX,
                  y: avgY,
                  score: avgScore,
                  name: kp.name
                };
              }
              
              // STRATEGY 3: Use anatomical positioning as fallback
              const anatomicalPosition = getAnatomicalPosition(index, imageSize);
              if (anatomicalPosition) {
                // Only log first few keypoints to avoid spam
                if (index < 3 || frameCount.current % 60 === 0) {
                  console.log(`🛠️ ANATOMICAL REPAIR: Using anatomical position for keypoint ${index} (${kp.name})`);
                }
                
                return {
                  ...kp,
                  x: anatomicalPosition.x,
                  y: anatomicalPosition.y,
                  score: anatomicalPosition.score,
                  name: kp.name
                };
              }
              
              // STRATEGY 4: Ultimate fallback - center with low confidence
              return {
                ...kp,
                x: imageSize.width / 2,
                y: imageSize.height / 2,
                score: 0.1,
                name: kp.name
              };
            }
            
            // Keypoint is valid, return as is
            return kp;
          });
        };
        
        // Enhanced 3D coordinate repair with better fallbacks
        const repairNaN3DCoordinates = (keypoints3D: any[]): any[] => {
          if (!keypoints3D || keypoints3D.length === 0) return [];
          
          // Count valid 3D keypoints
          const validKeypoints3D = keypoints3D.filter(kp => 
            !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.z)
          );
          
          // If we have no valid 3D keypoints, use default anatomical 3D positions
          if (validKeypoints3D.length === 0) {
            console.log('🛠️ NaN REPAIR: No valid 3D keypoints found, using anatomical 3D positions');
            return keypoints3D.map((kp, index) => {
              const anatomical3D = getAnatomical3DPosition(index);
              return {
                ...kp,
                x: anatomical3D.x,
                y: anatomical3D.y,
                z: anatomical3D.z,
                score: 0.1,
                visibility: 0.1
              };
            });
          }
          
          // If we have some valid keypoints, use them to help repair the invalid ones
          return keypoints3D.map((kp, index) => {
            if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.z)) {
              // Try to use bilateral symmetry for repair
              const symmetricKeypointIndex = getSymmetricKeypointIndex(index);
              if (symmetricKeypointIndex !== -1) {
                const symmetricKeypoint = keypoints3D[symmetricKeypointIndex];
                if (symmetricKeypoint && !isNaN(symmetricKeypoint.x) && !isNaN(symmetricKeypoint.y) && !isNaN(symmetricKeypoint.z)) {
                  // Use symmetric keypoint with mirroring around center (x-axis)
                  return {
                    ...kp,
                    x: -symmetricKeypoint.x, // Mirror x-coordinate
                    y: symmetricKeypoint.y,
                    z: symmetricKeypoint.z,
                    score: symmetricKeypoint.score * 0.8,
                    visibility: symmetricKeypoint.visibility * 0.8
                  };
                }
              }
              
              // Try to use adjacent keypoints for interpolation
              const adjacentIndices = getAdjacentKeypointIndices(index);
              const validAdjacents = adjacentIndices
                .map(idx => keypoints3D[idx])
                .filter(adjKp => adjKp && !isNaN(adjKp.x) && !isNaN(adjKp.y) && !isNaN(adjKp.z));
              
              if (validAdjacents.length > 0) {
                // Calculate average position from adjacent keypoints
                const avgX = validAdjacents.reduce((sum, adjKp) => sum + adjKp.x, 0) / validAdjacents.length;
                const avgY = validAdjacents.reduce((sum, adjKp) => sum + adjKp.y, 0) / validAdjacents.length;
                const avgZ = validAdjacents.reduce((sum, adjKp) => sum + adjKp.z, 0) / validAdjacents.length;
                const avgScore = validAdjacents.reduce((sum, adjKp) => sum + (adjKp.score || 0), 0) / validAdjacents.length * 0.7;
                const avgVisibility = validAdjacents.reduce((sum, adjKp) => sum + (adjKp.visibility || 0), 0) / validAdjacents.length * 0.7;
                
                return {
                  ...kp,
                  x: avgX,
                  y: avgY,
                  z: avgZ,
                  score: avgScore,
                  visibility: avgVisibility
                };
              }
              
              // Use anatomical 3D position as fallback
              const anatomical3D = getAnatomical3DPosition(index);
              return {
                ...kp,
                x: anatomical3D.x,
                y: anatomical3D.y,
                z: anatomical3D.z,
                score: 0.1,
                visibility: 0.1
              };
            }
            
            // Keypoint is valid, return as is
            return kp;
          });
        };
        
        // Apply NaN repairs
        processedKeypoints = repairNaNCoordinates(processedKeypoints);
        processedKeypoints3D = repairNaN3DCoordinates(processedKeypoints3D);
        
        // Log repair summary (reduced frequency)
        if (frameCount.current % 60 === 0) {
          const repairedKeypointsCount = processedKeypoints.filter(kp => 
            !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.score)
          ).length;
          
          const repairedKeypoints3DCount = processedKeypoints3D.filter(kp => 
            !isNaN(kp.x) && !isNaN(kp.y) && !isNaN(kp.z)
          ).length;
          
          console.log(`🎯 REPAIR SUMMARY: 2D: ${repairedKeypointsCount}/${processedKeypoints.length}, 3D: ${repairedKeypoints3DCount}/${processedKeypoints3D.length} keypoints valid`);
          
          // Validate sample keypoints
          if (processedKeypoints && processedKeypoints.length > 0) {
            const sampleKeypoint = processedKeypoints[0];
            console.log('🔍 PHASE 4 FIXED: Sample 2D keypoint validation:', {
              name: sampleKeypoint.name,
              x: sampleKeypoint.x?.toFixed(2),
              y: sampleKeypoint.y?.toFixed(2),
              score: sampleKeypoint.score?.toFixed(3),
              isValid: !isNaN(sampleKeypoint.x) && !isNaN(sampleKeypoint.y)
            });
          }
          
          if (processedKeypoints3D && processedKeypoints3D.length > 0) {
            const sample3D = processedKeypoints3D[0];
            console.log('🔍 PHASE 4 FIXED: Sample 3D keypoint validation:', {
              name: sample3D.name,
              x: sample3D.x?.toFixed(4),
              y: sample3D.y?.toFixed(4),
              z: sample3D.z?.toFixed(4),
              isValid: !isNaN(sample3D.x) && !isNaN(sample3D.y) && !isNaN(sample3D.z)
            });
          }
        }
        
      // Apply smoothing and stability filtering
      if (processedKeypoints && processedKeypoints.length > 0) {
        // Apply enhanced smoothing
        processedKeypoints = keypointsFilter.apply(processedKeypoints);
        
        // Apply stability filter
        const stabilityResult = stabilityFilter.apply(processedKeypoints);
        processedKeypoints = stabilityResult.keypoints;
        
        if (frameCount.current % 60 === 0) {
          console.log('✅ PHASE 4 FIXED: Applied pose stability filter:', {
            isStable: stabilityResult.isStable,
            confidence: stabilityResult.confidence.toFixed(3)
          });
        }
      }
      
      // Apply visibility smoothing to 3D landmarks
      if (processedKeypoints3D && processedKeypoints3D.length > 0) {
        processedKeypoints3D = visibilityFilter.apply(processedKeypoints3D);
      }
      
      // Create the processed pose with fixed coordinates
      const processedPose: poseDetection.Pose = {
        ...pose,
        keypoints: processedKeypoints,
        keypoints3D: processedKeypoints3D
      };
      
      if (frameCount.current % 60 === 0) {
        console.log('✅ PHASE 4 FIXED: BlazePose coordinate processing complete');
      }
      
      // Manually clean up any tensors that might have been created
      try {
        // Force garbage collection of any unused tensors
        tf.tidy(() => {});
      } catch (cleanupError) {
        console.warn('⚠️ Tensor cleanup error:', cleanupError);
      }
      
      return processedPose;
    } catch (processingError) {
      console.error('❌ PHASE 4 PROCESSING: Processing error:', processingError);
      
      // Even in case of error, try to return a valid pose
      try {
        // Basic error recovery - at least ensure no NaN values
        const safeKeypoints = pose.keypoints.map((kp, index) => {
          if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score)) {
            return {
              ...kp,
              x: imageSize.width / 2,
              y: imageSize.height / 2,
              score: 0.1,
              name: kp.name || `keypoint_${index}`
            };
          }
          return kp;
        });
        
        return {
          ...pose,
          keypoints: safeKeypoints,
          keypoints3D: pose.keypoints3D || []
        };
      } catch (recoveryError) {
        // Last resort - return original pose
        return pose;
      }
    }
  };
  
  // Helper function to get the symmetric keypoint index
  const getSymmetricKeypointIndex = (index: number): number => {
    // Map of symmetric keypoint pairs (left-right)
    const symmetricPairs: Record<number, number> = {
      1: 4, 4: 1, // left_eye_inner <-> right_eye_inner
      2: 5, 5: 2, // left_eye <-> right_eye
      3: 6, 6: 3, // left_eye_outer <-> right_eye_outer
      7: 8, 8: 7, // left_ear <-> right_ear
      9: 10, 10: 9, // mouth_left <-> mouth_right
      11: 12, 12: 11, // left_shoulder <-> right_shoulder
      13: 14, 14: 13, // left_elbow <-> right_elbow
      15: 16, 16: 15, // left_wrist <-> right_wrist
      17: 18, 18: 17, // left_pinky <-> right_pinky
      19: 20, 20: 19, // left_index <-> right_index
      21: 22, 22: 21, // left_thumb <-> right_thumb
      23: 24, 24: 23, // left_hip <-> right_hip
      25: 26, 26: 25, // left_knee <-> right_knee
      27: 28, 28: 27, // left_ankle <-> right_ankle
      29: 30, 30: 29, // left_heel <-> right_heel
      31: 32, 32: 31, // left_foot_index <-> right_foot_index
    };
    
    return symmetricPairs[index] !== undefined ? symmetricPairs[index] : -1;
  };
  
  // Helper function to get adjacent keypoint indices
  const getAdjacentKeypointIndices = (index: number): number[] => {
    // Map of adjacent keypoints for each keypoint
    const adjacencyMap: Record<number, number[]> = {
      0: [1, 2, 4, 5, 9, 10], // nose -> eyes, mouth
      1: [0, 2, 3], // left_eye_inner -> nose, left_eye, left_eye_outer
      2: [1, 3, 7], // left_eye -> left_eye_inner, left_eye_outer, left_ear
      3: [2, 7], // left_eye_outer -> left_eye, left_ear
      4: [0, 5, 6], // right_eye_inner -> nose, right_eye, right_eye_outer
      5: [4, 6, 8], // right_eye -> right_eye_inner, right_eye_outer, right_ear
      6: [5, 8], // right_eye_outer -> right_eye, right_ear
      7: [2, 3, 9, 11], // left_ear -> left_eye, left_eye_outer, mouth_left, left_shoulder
      8: [5, 6, 10, 12], // right_ear -> right_eye, right_eye_outer, mouth_right, right_shoulder
      9: [0, 7, 10], // mouth_left -> nose, left_ear, mouth_right
      10: [0, 8, 9], // mouth_right -> nose, right_ear, mouth_left
      11: [7, 13, 23], // left_shoulder -> left_ear, left_elbow, left_hip
      12: [8, 14, 24], // right_shoulder -> right_ear, right_elbow, right_hip
      13: [11, 15], // left_elbow -> left_shoulder, left_wrist
      14: [12, 16], // right_elbow -> right_shoulder, right_wrist
      15: [13, 17, 19, 21], // left_wrist -> left_elbow, left_pinky, left_index, left_thumb
      16: [14, 18, 20, 22], // right_wrist -> right_elbow, right_pinky, right_index, right_thumb
      17: [15, 19], // left_pinky -> left_wrist, left_index
      18: [16, 20], // right_pinky -> right_wrist, right_index
      19: [15, 17, 21], // left_index -> left_wrist, left_pinky, left_thumb
      20: [16, 18, 22], // right_index -> right_wrist, right_pinky, right_thumb
      21: [15, 19], // left_thumb -> left_wrist, left_index
      22: [16, 20], // right_thumb -> right_wrist, right_index
      23: [11, 24, 25], // left_hip -> left_shoulder, right_hip, left_knee
      24: [12, 23, 26], // right_hip -> right_shoulder, left_hip, right_knee
      25: [23, 27], // left_knee -> left_hip, left_ankle
      26: [24, 28], // right_knee -> right_hip, right_ankle
      27: [25, 29, 31], // left_ankle -> left_knee, left_heel, left_foot_index
      28: [26, 30, 32], // right_ankle -> right_knee, right_heel, right_foot_index
      29: [27, 31], // left_heel -> left_ankle, left_foot_index
      30: [28, 32], // right_heel -> right_ankle, right_foot_index
      31: [27, 29], // left_foot_index -> left_ankle, left_heel
      32: [28, 30], // right_foot_index -> right_ankle, right_heel
    };
    
    return adjacencyMap[index] || [];
  };
  
  // Helper function to get anatomical 3D positions
  const getAnatomical3DPosition = (index: number): { x: number; y: number; z: number } => {
    // Default 3D anatomical positions (normalized to [-1, 1] range)
    const anatomical3DMap: Record<number, { x: number; y: number; z: number }> = {
      // Face keypoints
      0: { x: 0, y: 0, z: -0.5 }, // nose
      1: { x: -0.1, y: 0.1, z: -0.5 }, // left_eye_inner
      2: { x: -0.2, y: 0.1, z: -0.4 }, // left_eye
      3: { x: -0.3, y: 0.1, z: -0.3 }, // left_eye_outer
      4: { x: 0.1, y: 0.1, z: -0.5 }, // right_eye_inner
      5: { x: 0.2, y: 0.1, z: -0.4 }, // right_eye
      6: { x: 0.3, y: 0.1, z: -0.3 }, // right_eye_outer
      7: { x: -0.3, y: 0, z: -0.2 }, // left_ear
      8: { x: 0.3, y: 0, z: -0.2 }, // right_ear
      9: { x: -0.1, y: -0.1, z: -0.5 }, // mouth_left
      10: { x: 0.1, y: -0.1, z: -0.5 }, // mouth_right
      
      // Upper body
      11: { x: -0.3, y: -0.3, z: -0.1 }, // left_shoulder
      12: { x: 0.3, y: -0.3, z: -0.1 }, // right_shoulder
      13: { x: -0.5, y: -0.0, z: -0.1 }, // left_elbow
      14: { x: 0.5, y: -0.0, z: -0.1 }, // right_elbow
      15: { x: -0.6, y: 0.2, z: -0.1 }, // left_wrist
      16: { x: 0.6, y: 0.2, z: -0.1 }, // right_wrist
      17: { x: -0.65, y: 0.25, z: -0.1 }, // left_pinky
      18: { x: 0.65, y: 0.25, z: -0.1 }, // right_pinky
      19: { x: -0.65, y: 0.2, z: -0.15 }, // left_index
      20: { x: 0.65, y: 0.2, z: -0.15 }, // right_index
      21: { x: -0.6, y: 0.15, z: -0.2 }, // left_thumb
      22: { x: 0.6, y: 0.15, z: -0.2 }, // right_thumb
      
      // Lower body
      23: { x: -0.2, y: -0.7, z: -0.1 }, // left_hip
      24: { x: 0.2, y: -0.7, z: -0.1 }, // right_hip
      25: { x: -0.25, y: -1.0, z: -0.1 }, // left_knee
      26: { x: 0.25, y: -1.0, z: -0.1 }, // right_knee
      27: { x: -0.25, y: -1.4, z: -0.1 }, // left_ankle
      28: { x: 0.25, y: -1.4, z: -0.1 }, // right_ankle
      29: { x: -0.25, y: -1.45, z: -0.2 }, // left_heel
      30: { x: 0.25, y: -1.45, z: -0.2 }, // right_heel
      31: { x: -0.25, y: -1.45, z: 0.1 }, // left_foot_index
      32: { x: 0.25, y: -1.45, z: 0.1 }, // right_foot_index
    };
    
    return anatomical3DMap[index] || { x: 0, y: 0, z: -1 };
  };

  // 🛠️ ANATOMICAL POSITION GENERATOR: Create realistic body positioning for back-facing poses
  // Pre-calculate height scaling to avoid performance issues
  const userHeightMeters = userHeight ? (userHeight.feet * 12 + userHeight.inches) * 0.0254 : 1.75; // Default to 5'9"
  const heightScaleFactor = userHeightMeters / 1.75; // Normalize to average height

  const getAnatomicalPosition = (keypointIndex: number, imageSize: { width: number; height: number }): { x: number; y: number; score: number } | null => {
    const centerX = imageSize.width / 2;
    const centerY = imageSize.height / 2;
    
    // Use pre-calculated height scaling (avoid recalculating for each keypoint)
    const headHeight = imageSize.height * 0.12 * heightScaleFactor; // Head is ~12% of body height
    const shoulderWidth = imageSize.width * 0.25 * heightScaleFactor; // Shoulders ~25% of frame width
    const torsoHeight = imageSize.height * 0.35 * heightScaleFactor; // Torso ~35% of height
    const legHeight = imageSize.height * 0.45 * heightScaleFactor; // Legs ~45% of height
    
    // BlazePose keypoint mapping for anatomical placement (USER HEIGHT PROPORTIONAL)
    const anatomicalMap: { [key: number]: { x: number; y: number; score: number } } = {
      // Face keypoints (low confidence for back-facing) - proportional to head size
      0: { x: centerX, y: centerY - torsoHeight * 0.8, score: 0.1 }, // nose
      1: { x: centerX - headHeight * 0.2, y: centerY - torsoHeight * 0.85, score: 0.1 }, // left_eye_inner
      2: { x: centerX - headHeight * 0.3, y: centerY - torsoHeight * 0.85, score: 0.1 }, // left_eye
      3: { x: centerX - headHeight * 0.4, y: centerY - torsoHeight * 0.85, score: 0.1 }, // left_eye_outer
      4: { x: centerX + headHeight * 0.2, y: centerY - torsoHeight * 0.85, score: 0.1 }, // right_eye_inner
      5: { x: centerX + headHeight * 0.3, y: centerY - torsoHeight * 0.85, score: 0.1 }, // right_eye
      6: { x: centerX + headHeight * 0.4, y: centerY - torsoHeight * 0.85, score: 0.1 }, // right_eye_outer
      7: { x: centerX - headHeight * 0.5, y: centerY - torsoHeight * 0.8, score: 0.1 }, // left_ear
      8: { x: centerX + headHeight * 0.5, y: centerY - torsoHeight * 0.8, score: 0.1 }, // right_ear
      9: { x: centerX - headHeight * 0.15, y: centerY - torsoHeight * 0.7, score: 0.1 }, // mouth_left
      10: { x: centerX + headHeight * 0.15, y: centerY - torsoHeight * 0.7, score: 0.1 }, // mouth_right
      
      // Body keypoints (higher confidence for back-facing) - proportional to user height
      11: { x: centerX - shoulderWidth * 0.5, y: centerY - torsoHeight * 0.5, score: 0.6 }, // left_shoulder
      12: { x: centerX + shoulderWidth * 0.5, y: centerY - torsoHeight * 0.5, score: 0.6 }, // right_shoulder
      13: { x: centerX - shoulderWidth * 0.7, y: centerY - torsoHeight * 0.1, score: 0.5 }, // left_elbow
      14: { x: centerX + shoulderWidth * 0.7, y: centerY - torsoHeight * 0.1, score: 0.5 }, // right_elbow
      15: { x: centerX - shoulderWidth * 0.8, y: centerY + torsoHeight * 0.2, score: 0.4 }, // left_wrist
      16: { x: centerX + shoulderWidth * 0.8, y: centerY + torsoHeight * 0.2, score: 0.4 }, // right_wrist
      17: { x: centerX - shoulderWidth * 0.3, y: centerY - torsoHeight * 0.6, score: 0.3 }, // left_pinky
      18: { x: centerX + shoulderWidth * 0.3, y: centerY - torsoHeight * 0.6, score: 0.3 }, // right_pinky
      19: { x: centerX - shoulderWidth * 0.25, y: centerY - torsoHeight * 0.65, score: 0.3 }, // left_index
      20: { x: centerX + shoulderWidth * 0.25, y: centerY - torsoHeight * 0.65, score: 0.3 }, // right_index
      21: { x: centerX - shoulderWidth * 0.28, y: centerY - torsoHeight * 0.55, score: 0.3 }, // left_thumb
      22: { x: centerX + shoulderWidth * 0.28, y: centerY - torsoHeight * 0.55, score: 0.3 }, // right_thumb
      23: { x: centerX - shoulderWidth * 0.35, y: centerY + torsoHeight * 0.4, score: 0.7 }, // left_hip
      24: { x: centerX + shoulderWidth * 0.35, y: centerY + torsoHeight * 0.4, score: 0.7 }, // right_hip
      25: { x: centerX - shoulderWidth * 0.4, y: centerY + legHeight * 0.5, score: 0.6 }, // left_knee
      26: { x: centerX + shoulderWidth * 0.4, y: centerY + legHeight * 0.5, score: 0.6 }, // right_knee
      27: { x: centerX - shoulderWidth * 0.45, y: centerY + legHeight * 0.9, score: 0.5 }, // left_ankle
      28: { x: centerX + shoulderWidth * 0.45, y: centerY + legHeight * 0.9, score: 0.5 }, // right_ankle
      29: { x: centerX - shoulderWidth * 0.5, y: centerY + legHeight * 0.95, score: 0.4 }, // left_heel
      30: { x: centerX + shoulderWidth * 0.5, y: centerY + legHeight * 0.95, score: 0.4 }, // right_heel
      31: { x: centerX - shoulderWidth * 0.4, y: centerY + legHeight * 0.92, score: 0.4 }, // left_foot_index
      32: { x: centerX + shoulderWidth * 0.4, y: centerY + legHeight * 0.92, score: 0.4 }, // right_foot_index
    };
    
    return anatomicalMap[keypointIndex] || null;
  };

  // 🛠️ ANATOMICAL KEYPOINT ESTIMATION: Estimate missing keypoints from body structure
  const estimateKeypointFromBody = (targetIndex: number, validKeypoints: any[]): any | null => {
    // BlazePose keypoint indices (simplified for critical body parts)
    const keypointMap: { [key: number]: string } = {
      11: 'left_shoulder', 12: 'right_shoulder',
      13: 'left_elbow', 14: 'right_elbow',
      15: 'left_wrist', 16: 'right_wrist',
      23: 'left_hip', 24: 'right_hip',
      25: 'left_knee', 26: 'right_knee',
      27: 'left_ankle', 28: 'right_ankle'
    };
    
    const findKeypoint = (name: string) => validKeypoints.find(kp => kp.name === name);
    
    // Try to estimate based on bilateral symmetry and anatomical relationships
    switch (targetIndex) {
      case 11: // left_shoulder
        const rightShoulder = findKeypoint('right_shoulder');
        const leftHip = findKeypoint('left_hip');
        if (rightShoulder && leftHip) {
          return {
            x: rightShoulder.x - 50, // Approximate shoulder width
            y: rightShoulder.y,
            score: 0.6,
            name: 'left_shoulder'
          };
        }
        break;
        
      case 23: // left_hip
        const rightHip = findKeypoint('right_hip');
        if (rightHip) {
          return {
            x: rightHip.x - 30, // Approximate hip width
            y: rightHip.y,
            score: 0.6,
            name: 'left_hip'
          };
        }
        break;
        
      case 25: // left_knee
        const leftHipPoint = findKeypoint('left_hip');
        const leftAnkle = findKeypoint('left_ankle');
        if (leftHipPoint && leftAnkle) {
          return {
            x: (leftHipPoint.x + leftAnkle.x) / 2,
            y: (leftHipPoint.y + leftAnkle.y) / 2,
            score: 0.6,
            name: 'left_knee'
          };
        }
        break;
    }
    
    return null;
  };

  // CRASH FIX: Lightweight basic coordinate fixing function
  const processBlazePoseWithBasicFixes = (pose: poseDetection.Pose, imageSize: { width: number; height: number }): poseDetection.Pose => {
    if (!pose || !pose.keypoints || pose.keypoints.length === 0) {
      return pose;
    }

    // Simple NaN repair without complex tensor operations
    const repairedKeypoints = pose.keypoints.map((kp, index) => {
      if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.score)) {
        return {
          ...kp,
          x: imageSize.width / 2,
          y: imageSize.height / 2,
          score: 0.1,
          name: kp.name || `keypoint_${index}`
        };
      }
      return kp;
    });

    // Simple 3D keypoint repair
    const repaired3DKeypoints = pose.keypoints3D ? pose.keypoints3D.map((kp, index) => {
      if (isNaN(kp.x) || isNaN(kp.y) || isNaN(kp.z)) {
        return {
          ...kp,
          x: 0,
          y: 0,
          z: 0,
          score: 0.1
        };
      }
      return kp;
    }) : [];

    return {
      ...pose,
      keypoints: repairedKeypoints,
      keypoints3D: repaired3DKeypoints
    };
  };

  // SECTION 3: Enhanced 3D coordinate processing function
  const processWorldLandmarks = async (
    worldTensor: tf.Tensor,
    config: WorldLandmarkConfig
  ): Promise<Keypoint[]> => {
    console.log('🔧 SECTION 3: Processing world landmarks from tensor');
    console.log('🔧 SECTION 3: World tensor shape:', worldTensor.shape);

    try {
      // Extract world coordinates from tensor [1, 117] → 39 × 3
      const worldData = await worldTensor.data();
      const worldLandmarks: Keypoint[] = [];

      if (worldData.length < config.numWorldLandmarks * config.coordinatesPerLandmark) {
        console.warn('🔧 SECTION 3: Insufficient world tensor data:', worldData.length);
        return [];
      }

      for (let i = 0; i < config.numWorldLandmarks; i++) {
        const baseIndex = i * config.coordinatesPerLandmark;

        const landmark: Keypoint = {
          x: worldData[baseIndex],     // X in world space
          y: worldData[baseIndex + 1], // Y in world space
          z: worldData[baseIndex + 2], // Z in world space
          score: 1.0, // World coordinates don't have visibility scores
          name: `world_landmark_${i}`
        };

        worldLandmarks.push(landmark);
      }

      console.log('🔧 SECTION 3: Extracted world landmarks:', worldLandmarks.length);

      // Apply world coordinate normalization
      const normalizedWorldLandmarks = normalizeWorldCoordinates(worldLandmarks, config);

      console.log('🔧 SECTION 3: Successfully processed world landmarks from tensor');
      return normalizedWorldLandmarks;

    } catch (error) {
      console.error('❌ SECTION 3: Error processing world landmark tensor:', error);
      return [];
    }
  };

  // SECTION 1: Enhanced detection pipeline demonstration
  const processDetectionPipeline = async (
    imageTensor: tf.Tensor,
    imageSize: { width: number; height: number }
  ): Promise<Detection[]> => {
    console.log('🔧 SECTION 1: Processing enhanced detection pipeline');

    try {
      // This demonstrates how the enhanced detection pipeline would work
      // in a full raw tensor implementation

      // Step 1: Run detection model (simulated)
      console.log('🔧 SECTION 1: Running detection model');

      // Step 2: Process detection tensors (simulated)
      const mockDetections: Detection[] = [{
        boundingBox: {
          xMin: 0.1 * imageSize.width,
          yMin: 0.1 * imageSize.height,
          width: 0.8 * imageSize.width,
          height: 0.8 * imageSize.height
        },
        score: 0.9,
        landmarks: []
      }];

      // Step 3: Apply enhanced non-maximum suppression
      console.log('🔧 SECTION 1: Applying enhanced NMS');
      const filteredDetections = nonMaxSuppression(mockDetections, BLAZEPOSE_NMS_CONFIG_ENHANCED);

      // Step 4: Remove detection letterbox padding
      console.log('🔧 SECTION 1: Removing detection letterbox');
      const padding = calculateDetectionLetterboxPadding(
        imageSize,
        { width: 128, height: 128 }
      );
      const adjustedDetections = removeDetectionLetterbox(filteredDetections, padding, imageSize);

      // Step 5: Validate detection bounds
      const validDetections = adjustedDetections.filter(detection =>
        validateDetectionBounds(detection, imageSize)
      );

      console.log('🔧 SECTION 1: Enhanced detection pipeline complete:', validDetections.length, 'detections');
      return validDetections;

    } catch (error) {
      console.error('❌ SECTION 1: Enhanced detection pipeline failed:', error);
      return [];
    }
  };

  // SECTION 3: Enhanced performance monitoring and optimization functions
  const updateEnhancedPerformanceMetrics = (processingTime: number) => {
    if (!BLAZEPOSE_PERFORMANCE_CONFIG.enableMemoryMonitoring) return;

    const memoryInfo = tf.memory();
    setPerformanceMetrics(prev => {
      const newFrameCount = prev.frameCount + 1;
      const newAverageTime = (prev.averageProcessingTime * prev.frameCount + processingTime) / newFrameCount;

      return {
        averageProcessingTime: newAverageTime,
        frameCount: newFrameCount,
        memoryUsage: memoryInfo.numBytes,
        tensorCount: memoryInfo.numTensors
      };
    });

    console.log('🔧 SECTION 3: Enhanced performance metrics:', {
      processingTime: processingTime.toFixed(1) + 'ms',
      avgTime: performanceMetrics.averageProcessingTime.toFixed(1) + 'ms',
      tensors: memoryInfo.numTensors,
      memory: (memoryInfo.numBytes / 1024 / 1024).toFixed(1) + 'MB'
    });
  };

  const performMemoryCleanup = () => {
    const now = Date.now();
    if (now - lastCleanupTimeRef.current < BLAZEPOSE_PERFORMANCE_CONFIG.memoryCleanupInterval) {
      return;
    }

    console.log('🔧 SECTION 3: Performing memory cleanup');

    // Clean up tensor cache if it's too large
    if (tensorCacheRef.current.size > BLAZEPOSE_PERFORMANCE_CONFIG.maxCachedTensors) {
      const entries = Array.from(tensorCacheRef.current.entries());
      const toRemove = entries.slice(0, entries.length - BLAZEPOSE_PERFORMANCE_CONFIG.maxCachedTensors);

      toRemove.forEach(([key, tensor]) => {
        tensor.dispose();
        tensorCacheRef.current.delete(key);
      });

      console.log('🔧 SECTION 3: Cleaned up', toRemove.length, 'cached tensors');
    }

    // Force garbage collection
    tf.tidy(() => {});

    lastCleanupTimeRef.current = now;
  };

  const getCachedTensor = (key: string): tf.Tensor | null => {
    if (!BLAZEPOSE_PERFORMANCE_CONFIG.enableTensorCaching) return null;
    return tensorCacheRef.current.get(key) || null;
  };

  const setCachedTensor = (key: string, tensor: tf.Tensor): void => {
    if (!BLAZEPOSE_PERFORMANCE_CONFIG.enableTensorCaching) return;

    // Clone tensor to avoid disposal issues
    const clonedTensor = tensor.clone();
    tensorCacheRef.current.set(key, clonedTensor);

    // Trigger cleanup if cache is getting too large
    if (tensorCacheRef.current.size > BLAZEPOSE_PERFORMANCE_CONFIG.maxCachedTensors) {
      performMemoryCleanup();
    }
  };

  // SECTION 3: World coordinate normalization function
  const normalizeWorldCoordinates = (
    worldLandmarks: Keypoint[],
    config: WorldLandmarkConfig
  ): Keypoint[] => {
    console.log('🔧 SECTION 3: Normalizing world coordinates');

    return worldLandmarks.map((landmark, index) => {
      // Apply world coordinate normalization
      const normalizedLandmark: Keypoint = {
        x: landmark.x,
        y: landmark.y,
        z: landmark.z || 0,
        score: landmark.score,
        name: landmark.name
      };

      // Apply scaling and normalization
      if (config.normalizeZ && normalizedLandmark.z) {
        normalizedLandmark.z = normalizedLandmark.z / config.normalizeZ;
      }

      // Apply world coordinate scaling
      normalizedLandmark.x *= config.worldCoordinateScale;
      normalizedLandmark.y *= config.worldCoordinateScale;
      if (normalizedLandmark.z) {
        normalizedLandmark.z *= config.worldCoordinateScale;
      }

      // Validate coordinates and handle NaN values
      if (isNaN(normalizedLandmark.x) || isNaN(normalizedLandmark.y) || isNaN(normalizedLandmark.z || 0)) {
        console.warn(`🔧 SECTION 3: NaN detected in world landmark ${index}, using fallback`);
        normalizedLandmark.x = 0;
        normalizedLandmark.y = 0;
        normalizedLandmark.z = 0;
        normalizedLandmark.score = 0;
      }

      // Clamp Z-coordinates to reasonable range
      if (normalizedLandmark.z !== undefined) {
        const [minZ, maxZ] = config.worldCoordinateRange;
        normalizedLandmark.z = Math.max(minZ, Math.min(maxZ, normalizedLandmark.z));
      }

      return normalizedLandmark;
    });
  };

  const resetDetector = () => {
    if (detector && typeof detector.reset === 'function') {
      console.log('🔄 PHASE 4 ENHANCED: Resetting BlazePose detector and filters');
      try {
        detector.reset();
        keypointsFilter.reset();
        visibilityFilter.reset();
        stabilityFilter.reset();
        console.log('✅ BlazePose detector and filters reset successful');
      } catch (resetError) {
        console.warn('⚠️ Error resetting BlazePose detector:', resetError);
      }
    }
  };

  return {
    detector,
    isInitialized,
    debugInfo,
    setDebugInfo,
    detectPoses,
    resetDetector,
    enableEnhancedTensorProcessing,
    setEnableEnhancedTensorProcessing,
    // SECTION 3: Expose enhanced 3D processing functions for testing
    processWorldLandmarks,
    normalizeWorldCoordinates,
    // SECTION 1: Expose enhanced detection pipeline for testing
    processDetectionPipeline,
    // SECTION 3: Expose performance monitoring and optimization functions
    performanceMetrics,
    updateEnhancedPerformanceMetrics,
    performMemoryCleanup,
    getCachedTensor,
    setCachedTensor
  };
};
