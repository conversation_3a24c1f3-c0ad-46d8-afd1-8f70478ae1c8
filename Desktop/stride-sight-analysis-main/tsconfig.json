{"files": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.node.json"}], "compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "noImplicitAny": false, "noUnusedParameters": false, "skipLibCheck": true, "allowJs": true, "noUnusedLocals": false, "strictNullChecks": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true}}